import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import FluentUI 1.0
import "qrc:/SimpleUI/qml"
import "qrc:/qml/gv"

Item {
    id: variable_input

    property string deviceName: ""
    property string owned: ""
    property string type: ""
    property string code: "ST"
    property var dataTypeList: []
    property var scopeList: []
    property var extendVariable: []
    property var localVariable: []
    property var allVariableType: []
    property string search_text: ""
    property bool isCheckedAll: false

    signal variableModify(string action, string varname)

    VariableSelect {
        id: variable_select

        x: (root.width - variable_select.width) * 0.2
        y: (root.height - variable_select.height) * 0.2

        targetowned: variable_input.owned
        targettype: variable_input.type
        deviceName: variable_input.deviceName

        onVariableModify: {
            variableModify("added", varname)
        }
    }

    O_TreeTable {
        id: tree_table
        anchors.fill: parent
        cellHeight: 30
        columnSource: [
            {
                title: "序号",
                dataIndex: "_key",
                width: 60,
                minimumWidth: 60,
                maximumWidth: 60,
                readOnly: true
            },
            {
                title: tree_table.customItem(column_checbox),
                dataIndex: 'isChecked',
                width: 50,
                minimumWidth: 50,
                maximumWidth: 50
            },
            {
                title: "作用域",
                dataIndex: "scope",
                editDelegate: scope_combo,
                width: 100
            },
            {
                title: "名称",
                dataIndex: "name",
                editDelegate: name_text_field,
                width: (variable_input.width - 210) / 6
            },
            {
                title: "数据类型",
                dataIndex: "datatype",
                editDelegate: data_type_combo,
                width: (variable_input.width - 210) / 6
            },
            {
                title: "数组长度",
                dataIndex: "arraylength",
                editDelegate: array_length_spin,
                width: (variable_input.width - 210) / 6
            },
            {
                title: "地址",
                dataIndex: "address",
                editDelegate: address_text_field,
                width: (variable_input.width - 210) / 6
            },
            {
                title: "初始值",
                dataIndex: "initialvalue",
                editDelegate: initial_value_text_field,
                width: (variable_input.width - 210) / 6
            },
            {
                title: "描述",
                dataIndex: "description",
                editDelegate: description_text_field,
                width: (variable_input.width - 210) / 6
            }
        ]
    }

    Component {
        id: column_checbox
        Item {
            S_CheckBox {
                anchors.centerIn: parent
                checked: variable_input.isCheckedAll
                Layout.alignment: Qt.AlignVCenter
                onCheckedChanged: {
                    if (checked === variable_input.isCheckedAll) return
                    variable_input.isCheckedAll = checked
                    Qt.callLater(() => {
                        tree_table.checkAll(checked, variable_input.isCheckedAll)
                    })
                }
            }
        }
    }

    Component {
        id: com_checkbox
        Item {
            S_CheckBox {
                anchors.centerIn: parent
                checked: rowModel.checked
                Layout.alignment: Qt.AlignVCenter
                onCheckedChanged: {
                    if (checked === variable_input.isCheckedAll) return
                    tree_table.checkRow(row, checked)
                    Qt.callLater(() => {
                        variable_input.isCheckedAll = tree_table.selectionModel().length === tree_table.count()
                    })
                }
            }
        }
    }

    Component {
        id: scope_combo
        ComboBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            editText: display
            font.pixelSize: 14
            model: variable_input.scopeList
            visible: variable_input.scopeList.length === 1 ? false : checkEditableFromScope(display)
            currentIndex: variable_input.scopeList.findIndex(scope => {
                return scope === display
            })
            onActivated: {
                const param = {
                    vid: rowData.vid,
                    scope: editText,
                    name: rowData.name,
                    datatype: rowData.datatype,
                    dataTypeID: rowData.dataTypeID,
                    arraylength: rowData.arraylength,
                    address: rowData.address,
                    initialvalue: rowData.initialvalue,
                    retain: rowData.retain,
                    description: rowData.description
                }
                if (modifyVariableValue(param)) {
                    variableModify("modify", rowData.name)
                    tableView.closeEditor()
                    variable_input.getDataBind()
                } else {
                    message_dialog.show("变量作用域修改失败！")
                }
            }
        }
    }

    Component {
        id: name_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display) || ""
            font.pixelSize: 14
            visible: checkEditableFromScope(rowData.scope)
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus && visible) {
                    if (text.length <= 3) {
                        messageDialog.show("名称长度不得低于3")
                        forceActiveFocus()
                    } else if (haveSameName(rowData.vid, text)) {
                        messageDialog.show("有重名，修改变量失败")
                        tableView.closeEditor()
                    } else {
                        const param = {
                            vid: rowData.vid,
                            scope: rowData.scope,
                            name: text,
                            datatype: rowData.datatype,
                            dataTypeID: rowData.dataTypeID,
                            arraylength: rowData.arraylength,
                            address: rowData.address,
                            initialvalue: rowData.initialvalue,
                            retain: rowData.retain,
                            description: rowData.description
                        }
                        if (modifyVariableValue(param)) {
                            variableModify("modify", rowData.name)
                            tableView.closeEditor()
                            variable_input.getDataBind()
                        } else {
                            messageDialog.show("修改变量名失败")
                            tableView.closeEditor()
                        }
                    }
                }
            }
        }
    }

    Component {
        id: data_type_combo
        ComboBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            textRole: "name"
            valueRole: "vid"
            editText: display
            font.pixelSize: 14
            model: variable_input.dataTypeList
            visible: checkEditableFromScope(rowData.scope)
            currentIndex: variable_input.dataTypeList.findIndex(dataType => {
                return dataType.name === display
            })
            onActiveFocusChanged: {
                if (!activeFocus)
                    tableView.closeEditor()
            }
            onActivated: {
                const param = {
                    vid: rowData.vid,
                    scope: rowData.scope,
                    name: rowData.name,
                    datatype: editText,
                    dataTypeID: currentValue,
                    arraylength: rowData.arraylength,
                    address: rowData.address,
                    initialvalue: rowData.initialvalue,
                    retain: rowData.retain,
                    description: rowData.description
                }
                if (modifyVariableValue(param)) {
                    variableModify("modify", rowData.name)
                    tableView.closeEditor()
                    variable_input.getDataBind()
                } else {
                    message_dialog.show("变量类型修改失败！")
                }
            }
        }
    }

    Component {
        id: array_length_spin
        SpinBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            from: 1
            to: 20000
            editable: true
            value: Number(display)
            font.pixelSize: 14
            visible: checkEditableFromScope(rowData.scope) && rowData.datatype !== "BOOL" && variable_input.code !== "C"
            onActiveFocusChanged: {
                if (!activeFocus)
                    tableView.closeEditor()
            }
            onValueModified: {
                if (value !== rowData.arraylength) {
                    const param = {
                        vid: rowData.vid,
                        scope: rowData.scope,
                        name: rowData.name,
                        datatype: rowData.datatype,
                        dataTypeID: rowData.dataTypeID,
                        arraylength: value,
                        address: rowData.address,
                        initialvalue: value > 1 ? "" : rowData.initialvalue,
                        retain: rowData.retain,
                        description: rowData.description
                    }
                    if (modifyVariableValue(param)) {
                        variableModify("modify", rowData.name)
                        variable_input.getDataBind()
                    } else {
                        messageDialog.show("修改数组长度失败")
                        tableView.closeEditor()
                    }
                }
            }
        }
    }

    Component {
        id: address_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14
            enabled: rowData.addressEnabled
            // visible: checkEditableFromScopeForAddressAndInitialValue(rowData.scope)
            visible: false
            validator: RegularExpressionValidator {
                regularExpression: GlobalVariable.address_regular
            }
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus && visible) {
                    const param = {
                        vid: rowData.vid,
                        scope: rowData.scope,
                        name: rowData.name,
                        datatype: rowData.datatype,
                        dataTypeID: rowData.dataTypeID,
                        arraylength: rowData.arraylength,
                        address: text,
                        initialvalue: rowData.initialvalue,
                        retain: rowData.retain,
                        description: rowData.description
                    }
                    if (modifyVariableValue(param)) {
                        variableModify("modify", rowData.name)
                        tableView.closeEditor()
                        variable_input.getDataBind()
                    } else {
                        messageDialog.show("修改变量地址失败")
                        tableView.closeEditor()
                    }
                }
            }
        }
    }

    Component {
        id: initial_value_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14
            enabled: rowData.valueEnabled
            visible: checkEditableFromScopeForAddressAndInitialValue(rowData.scope) && !rowData.address
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus && visible) {
                    const param = {
                        vid: rowData.vid,
                        scope: rowData.scope,
                        name: rowData.name,
                        datatype: rowData.datatype,
                        dataTypeID: rowData.dataTypeID,
                        arraylength: rowData.arraylength,
                        address: rowData.address,
                        initialvalue: text,
                        retain: rowData.retain,
                        description: rowData.description
                    }
                    if (modifyVariableValue(param)) {
                        variableModify("modify", rowData.name)
                        tableView.closeEditor()
                        variable_input.getDataBind()
                    } else {
                        messageDialog.show("修改变量初始值失败")
                        tableView.closeEditor()
                    }
                }
            }
        }
    }

    Component {
        id: description_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14
            visible: checkEditableFromScope(rowData.scope)
            maximumLength: 20
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus && visible) {
                    const param = {
                        vid: rowData.vid,
                        scope: rowData.scope,
                        name: rowData.name,
                        datatype: rowData.datatype,
                        dataTypeID: rowData.dataTypeID,
                        arraylength: rowData.arraylength,
                        address: rowData.address,
                        initialvalue: rowData.initialvalue,
                        retain: rowData.retain,
                        description: text
                    }
                    if (modifyVariableValue(param)) {
                        variableModify("modify", rowData.name)
                        tableView.closeEditor()
                        variable_input.getDataBind()
                    } else {
                        messageDialog.show("修改变量描述失败")
                        tableView.closeEditor()
                    }
                }
            }
        }
    }


    FluContentDialog {
        id: message_dialog

        property var okfunc
        property var nofunc

        title: qsTr("Tip") + (trans ? trans.transString : "")
        message: qsTr("Input Error") + (trans ? trans.transString : "")
        negativeText: "取消"
        positiveText: "确定"
        buttonFlags: FluContentDialogType.PositiveButton
        onPositiveClicked: {
            if (okfunc) {
                okfunc()
            }
        }
        onNegativeClicked: {
            if (nofunc) {
                nofunc()
            }
        }

        function show(caption, funcok, funcno, type = "info") {
            messageDialog.okfunc = funcok
            messageDialog.nofunc = funcno
            if (type === "info") {
                messageDialog.buttonFlags = FluContentDialogType.PositiveButton
            } else if (type === "confirm") {
                messageDialog.buttonFlags = FluContentDialogType.NegativeButton
                    | FluContentDialogType.PositiveButton
            }
            messageDialog.message = caption
            messageDialog.open()
        }
    }

    function getDataBind() {
        allVariableType = []
        localVariable = []
        extendVariable = []
        variable_select.filterSelected = []
        allVariableType = VariableManage.getAllVariableType(variable_input.deviceName)
        const res = VariableManage.getVariableList(variable_input.deviceName, variable_input.owned, variable_input.type)
        let variablelist = []

        res.forEach((item, index) => {
            if (search_text) {
                if (item.name.toUpperCase().indexOf(search_text) > -1) {
                    variablelist.push({
                        _key: index + 1,
                        isChecked: tree_table.customItem(com_checkbox),
                        vid: item.vid,
                        scope: item.scope,
                        owned: item.owned,
                        type: item.type,
                        name: item.name,
                        datatype: item.dataType,
                        dataTypeID: item.dataTypeID,
                        retain: item.isRetained,
                        arraylength: item.arrayLength,
                        address: item.address,
                        initialvalue: item.initialValue,
                        description: item.description,
                        addressEnabled: !item.initialValue,
                        valueEnabled: !item.address && item.arrayLength === 1
                    })
                }
            } else {
                variablelist.push({
                    _key: index + 1,
                    isChecked: tree_table.customItem(com_checkbox),
                    vid: item.vid,
                    scope: item.scope,
                    owned: item.owned,
                    type: item.type,
                    name: item.name,
                    datatype: item.dataType,
                    dataTypeID: item.dataTypeID,
                    retain: item.isRetained,
                    arraylength: item.arrayLength,
                    address: item.address,
                    initialvalue: item.initialValue,
                    description: item.description,
                    addressEnabled: !item.initialValue,
                    valueEnabled: !item.address && item.arrayLength === 1
                })
            }
            if (variable_input.scopeList.indexOf(item.scope) !== -1) {
                const rowI = allVariableType.find(function (type) {
                    return type.vid === item.dataTypeID
                })
                item.attribute = rowI.type
                item.mainId = rowI.mainID
                localVariable.push(item)
            } else {
                extendVariable.push(item)
            }
            variable_select.filterSelected.push(item.name)
        })
        tree_table.dataSource = variablelist
        // emit: declareChanged()
    }

    function findIndexInListFromdataTypeID(list, dataTypeID) {
        if (list) {
            for (var i = 0; i < list.length; i++) {
                if (list[i].vid === dataTypeID)
                    return i
            }
        }
        return 0
    }

    //根据作用域判断是否可编辑
    function checkEditableFromScope(itemScope) {
        return scopeList.indexOf(itemScope) > -1
    }

    //根据作用域判断是否显示编辑框
    function checkEditableFromScopeForAddressAndInitialValue(itemScope) {
        return scopeList.indexOf(itemScope) > -1 && (
            variable_input.type === "PROGRAM" ||
            variable_input.type === "Global" ||
            variable_input.type === "IO" ||
            variable_input.type === "M"
        );
    }

    //是否有重名
    function haveSameName(vid, name) {
        const data = VariableManage.getFunctionBlockList(deviceName, owned, type)
        for (let i = 0; i < data.length; i++) {
            if (data[i].referenceName === name)
                return true
        }
        for (let i = 0; i < tree_table.count(); i++) {
            let obj = tree_table.getRow(i)
            if (obj["vid"] !== vid && obj["name"] === name)
                return true
        }
        return false
    }

    //修改变量
    function modifyVariableValue(rowObj) {
        if (rowObj["vid"] === undefined)
            return true
        const result = VariableManage.modifyVariable(
            rowObj["vid"],
            rowObj["scope"],
            rowObj["name"],
            rowObj["datatype"],
            rowObj["dataTypeID"],
            rowObj["arraylength"],
            rowObj["address"],
            rowObj["initialvalue"],
            rowObj["retain"],
            rowObj["description"]
        ).trim() === ""
        return result
    }

    //搜索变量
    function searchVariable(text) {
        const search = String(text).trim().toUpperCase()
        if (search !== "") {
            search_text = search
            getDataBind()
            tree_table.view.contentY = 0
        } else {
            search_text = ""
            getDataBind()
        }
    }

    function insertQueto() {
        const insertList = tree_table.selectionModel().map(item => {
            return item.data
        })

        if (insertList.length === 0) {
            messageDialog.show("请选择要插入的变量")
            return
        }
        // emit: quoteChanged({variable_quote: insertList})
    }

    //引用变量
    function quoteVariable() {
        variable_select.open()
    }

    //添加变量
    function addVariable() {
        let haveEmpty = false
        let variableName = "localVar"
        // 变量名后缀
        let variableNameSuffix = GlobalVariable.getRandomString(5)
        variableName = "LocalVar_" + variableNameSuffix
        if (!haveEmpty) {
            //添加
            if (VariableManage.addVariable(
                variable_input.deviceName,
                variable_input.scopeList[0],
                variable_input.owned,
                variable_input.type,
                variableName,
                variable_input.dataTypeList[0].name,
                1,
                "")) {
                variableModify("added", variableName)
                getDataBind()
                tree_table.view.contentY = tree_table.view.contentHeight > tree_table.view.height ? (tree_table.view.contentHeight - tree_table.view.height + 30) : 0
            } else {
                messageDialog.show("添加变量失败")
            }
        }
    }

    //删除变量
    function deleteVariable() {
        const delete_list = tree_table.selectionModel()
        if (delete_list.length === 0) {
            messageDialog.show("请选择要删除的变量")
            return
        } else {
            delete_list.forEach(item => {
                if (!VariableManage.deleteVariable(item.data.vid)) {
                    messageDialog.show("删除变量失败")
                    return
                }
                variableModify("delete", item.data.name)
            })
        }
        getDataBind()
        tree_table.view.contentY = 0
        variable_input.isCheckedAll = tree_table.selectionModel().length === tree_table.count()
    }

    Component.onCompleted: {
        variable_select.quoteVariableed.connect(variable_input.getDataBind)
    }
}