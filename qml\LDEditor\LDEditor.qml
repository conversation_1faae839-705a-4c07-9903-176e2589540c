﻿import QtQuick 2.15
import QtQuick.Dialogs 1.3
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import QtQuick.Layouts 1.15
import "qrc:/qml/gv"
import "qrc:/qml/skin"
import "qrc:/qml/control/common"
import "qrc:/qml/control/menu"
import "qrc:/qml/variableshow"

Rectangle {
    id: control
    clip: true
    color: "white"
    anchors.fill: parent

    // 配置
    property var config: LDEditorConfiger
    // 文件基本信息
    property var planInfo: ldManage.getPlanInfo(control.fileKey)
    
    property string deviceName: ""
    property string owned: ""
    property string type: ""
    // 文件名称
    property string fileKey: ""
    // 文件类型
    property string fileType: "LD"

    // 网络列表
    property var netWorkList: []

    // 当前所在的网络号
    property int netWorkNumber: -1
    // 记录当前选中的网络中对应的start块元件序号
    property int startBlockNumber: -1
    // 当前选中的块元件类型
    property var currentSelectBlockType
    // 当前选中的块元件数据
    property var currentSelectBolckData
    // 当前选中的块元件数据是否是最大的XPos
    property bool currentSelectBolckDataIsMaxXPos: false

    // 是否显示坐标块
    property bool isShowPositionBlocks: false
    // 是否需要保存文件
    property bool isConserve: false
    // 是否是调试模式
    property bool isTest: true
    // 当前激活焦点的组件
    property string activeFocusItem: ""
    // 当前打开的菜单
    property string currentOpenMenu: ""

    // 块元件文本高度
    property int funBlockTextHeight: config.cellHeight
    // 引脚块高度
    property int pinHeight: config.cellHeight
    // 坐标文本高度
    property int posTextHeight: config.cellHeight
    // 默认的单元格中间高度
    property int cellCentreHeight: config.cellHeight / 2

    // 连接全局快捷键信号对象
    Connections {
        target: shortcutController

        // 复制
        function onCopy(handler)
        {
            if(fileName === activeFile)
            {
                copyOrCutType = currentSelectBlockType
                handler(multiShortcutHandler.copy())
                getNetWork()
            }
        }

        // 剪切
        function onShear(handler)
        {
            if(fileName === activeFile)
            {
                // 清除剪切标识
                shortcutController.clearShearContent()
                copyOrCutType = currentSelectBlockType
                handler(multiShortcutHandler.shear())
                getNetWork(false)
                // 剪切之后是会将当前选中的添加剪切标识并将选中移除
                currentSelectBlockType = ""
                currentSelectBolckData = ""
            }
        }

        // 清除剪切标识
        function onClearShear()
        {
            // 刷新当前编辑器中所有的网络数据
            analysisNetWorkData(populateNetWorkConnections(ldManage.getAllInfo(fileKey)))
        }

        // 粘贴
        function onPaste(handler)
        {
            if(fileName === activeFile)
            {
                if(multiShortcutHandler.rightPaste(handler))
                {
                    getNetWork()
                }
            }
        }

        // 左粘贴
        function onLeftPaste(handler)
        {
            if(fileName === activeFile)
            {
                if(multiShortcutHandler.leftPaste(handler))
                {
                    getNetWork()
                }
            }
        }

        // 下粘贴
        function onLowerPaste(handler)
        {
            if(fileName === activeFile)
            {
                if(multiShortcutHandler.lowerPaste(handler))
                {
                    getNetWork()
                }
            }
        }

        // 撤销
        function onUndo(handler)
        {
            if(fileName === activeFile)
            {
                multiShortcutHandler.undo(handler)
            }
        }

        // 反撤销
        function onRedo(handler)
        {
            if(fileName === activeFile)
            {
                multiShortcutHandler.redo(handler)
            }
        }

        //删除
        function onDel()
        {
            if(fileName === activeFile)
            {
                multiShortcutHandler.del()
            }
        }

        // 多输入
        function onMultipleInput()
        {
            if(fileName === activeFile)
            {
                multiShortcutHandler.multipleInput()
            }
        }

        // 多输出
        function onMultipleOutput()
        {
            if(fileName === activeFile)
            {
                multiShortcutHandler.multipleOutput()
            }
        }

        // 置反
        function onReverse()
        {
            if(fileName === activeFile)
            {
                multiShortcutHandler.reverse()
            }
        }

        // 置位/复位
        function onSetOrReset()
        {
            if(fileName === activeFile)
            {
                multiShortcutHandler.setOrReset()
            }
        }
    }

    //画布窗口
    Rectangle {
        id: root
        anchors.fill: parent
        antialiasing: true
        smooth: true
        color: "transparent"

        // 非Flickable所在的内容区域的时候滚动
        WheelHandler {
            acceptedDevices: PointerDevice.Mouse
            onWheel: event => {
                         scale(event)
                     }
        }

        Flickable {
            id: flick
            anchors.fill: parent
            // 禁用回弹效果
            boundsBehavior: Flickable.StopAtBounds
            contentWidth: contentItem.width * (scaleFactor / 100) + 50
            contentHeight: contentItem.height * (scaleFactor / 100)
            interactive: false

            // 垂直滚动条
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                visible: flick.height < flick.contentHeight
                opacity: 1.0
                active: true
            }

            // 水平滚动条
            ScrollBar.horizontal: ScrollBar {
                policy: ScrollBar.AlwaysOn
                visible: flick.width < flick.contentWidth
                opacity: 1.0
                active: true
            }

            // 在Flickable内容区域中滚动
            WheelHandler {
                acceptedDevices: PointerDevice.Mouse
                onWheel: event => {
                             scale(event)
                         }
            }

            Item {
                id: contentItem
                width: Math.max(mainDesc.width, columnContent.width)
                height: mainDesc.height + config.defaultMargin * 4 + columnContent.height
                // 缩放比例
                scale: (scaleFactor / 100)
                // 以顶部和左边为原点进行缩放
                transformOrigin: Item.TopLeft
                // 将内容以图片的方式进行缩放,会导致最终的结果模糊
                // layer.enabled: true
                // layer.smooth: true
                // 缩放动画
                Behavior on scale {
                    NumberAnimation {
                        duration: 200
                    }
                }
                //全局注释窗口
                Rectangle {
                    id: mainDesc
                    anchors {
                        left: parent.left
                        leftMargin: config.defaultMargin
                        top: parent.top
                        topMargin: config.defaultMargin * 2
                    }
                    height: config.defaultDescHeight
                    width: control.width
                    Row {
                        anchors.fill: parent
                        spacing: 10

                        Text {
                            id: fileNameText
                            text: planInfo.Name + " :"
                            font.pixelSize: config.fontPixelSize
                            verticalAlignment: Text.AlignVCenter
                        }

                        Rectangle {
                            height: parent.height
                            width: parent.width - fileNameText.width - config.defaultMargin - 10
                            //文本编辑
                            Flickable {
                                id: flickText
                                anchors.fill: parent
                                contentWidth: editComment.paintedWidth
                                contentHeight: editComment.paintedHeight
                                clip: true
                                function ensureVisible(r) {
                                    if (contentX >= r.x)
                                        contentX = r.x
                                    else if (contentX + width <= r.x + r.width)
                                        contentX = r.x + r.width - width
                                    if (contentY >= r.y)
                                        contentY = r.y
                                    else if (contentY + height <= r.y + r.height)
                                        contentY = r.y + r.height - height
                                }
                                TextEdit {
                                    id: editComment
                                    width: flickText.width
                                    text: control.planInfo ? control.planInfo.Comment : ""
                                    font.pixelSize: config.fontPixelSize
                                    color: "#808097"
                                    textFormat: TextEdit.PlainText
                                    wrapMode: TextEdit.Wrap
                                    selectByMouse: true
                                    selectionColor: config.defaultSelectionColor
                                    selectedTextColor: config.defaultSelectedTextColor
                                    onEditingFinished: {
                                        if (editComment.text !== planInfo.Comment) {
                                            planInfo.Comment = editComment.text
                                            ldManage.updateAuthorComment(
                                                        control.fileKey,
                                                        planInfo.Author,
                                                        planInfo.Comment)
                                            getNetWork()
                                            updateFileFlag(fileKey, true)
                                        }
                                    }
                                    onCursorRectangleChanged: flickText.ensureVisible(
                                                                  cursorRectangle)
                                }
                            }
                        }
                    }
                }

                Column {
                    id: columnContent
                    anchors {
                        left: mainDesc.left
                        leftMargin: 0
                        top: mainDesc.bottom
                        topMargin: config.defaultMargin * 2
                    }
                    spacing: 5
                    Repeater {
                        model: netWorkList.length
                        LDNetwork {
                            mainControl: control
                            networkData: netWorkList[index]
                        }
                    }
                }
            }
        }
    }

    TapHandler {
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        onTapped: {
            activeFocusItem = ""
            currentSelectBlockType = ""
            currentSelectBolckData = {}
            netWorkNumber = -1
            parent.forceActiveFocus()
            let positionInRoot = mapToItem(control, eventPoint.position.x,
                                           eventPoint.position.y)
            if (eventPoint.event.button === Qt.RightButton) {
                control.showMenu(positionInRoot.x, positionInRoot.y, "EDITOR")
            }
        }
    }

    // 快捷键组件
    LDMultiShortcuts {
        id: multiShortcutHandler
    }

    property bool isInit: false

    function getDataBind() {
        isInit = true
        ldManage.fixLDFileConnections(fileKey)
    }

    // 监听网络组件变化
    Connections {
        target: ldManage
        function onFileChanged(fileKey) {
            if(fileKey !== control.fileKey)
            {
                return
            }
            // 让整个编辑器获取到焦点,从而让其它组件失去焦点
            // 如果不让其它组件失去焦点的情况下,在选中块元件的时候
            // 对块元件进行快捷键操作导致块元件的类型改变了
            // 从而与选中的时候的类型不一致了,就会导致快捷键不生效了
            control.focus = true
            currentSelectBlockType = ""
            currentSelectBolckData = ""
            getNetWork()

            if (isInit) {
                isInit = false
                updateFileFlag(fileKey, false)
            } else {
                updateFileFlag(fileKey, true)
            }
        }
    }

    Menu {
        id: editorMenu
        width: 200
        topPadding: 10
        bottomPadding: 10
        leftPadding: 10
        rightPadding: 10
        background: Rectangle {
            color: "#f2f2f2"
            border.width: 0
            layer.enabled: true
            layer.effect: DropShadow {
                radius: 4
                samples: 9
                color: "#8f8f8f"
                horizontalOffset: 2
                verticalOffset: 2
            }
        }

        // 头节
        Shortcut {
            sequence: "N"
            enabled: editorMenu.opened
            onActivated: {
                if (editorMenu.opened) {
                    addHeadNetWork()
                    editorMenu.close()
                }
            }
        }

        // 尾节
        Shortcut {
            sequence: "W"
            enabled: editorMenu.opened
            onActivated: {
                if (editorMenu.opened) {
                    addTailNetWork()
                    editorMenu.close()
                }
            }
        }

        Shortcut {
            sequence: "S"
            enabled: editorMenu.opened
            onActivated: {
                // if (editorMenu.opened && isConserve) {
                //     ldManage.saveFile(fileKey)
                //     updateFileFlag(false)
                //     editorMenu.close()
                // }
                if (editorMenu.opened) {
                    ldManage.saveFile(fileKey)
                    updateFileFlag(fileKey, false)
                    editorMenu.close()
                }
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Head Node(N)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.hovered ? "#024184" : "#545454"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                addHeadNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: netWorkList.length > 0
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Tail Node(W)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                addTailNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: netWorkList.length > 0
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Debugging Mode")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                isTest = !isTest
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: netWorkList.length > 0
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Save To File(S)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                // if (isConserve) {
                    ldManage.saveFile(fileKey)
                    updateFileFlag(fileKey, false)
                // }
            }
        }
    }

    Menu {
        id: networkMenu
        width: 200
        topPadding: 10
        bottomPadding: 10
        leftPadding: 10
        rightPadding: 10
        background: Rectangle {
            color: "#f2f2f2"
            border.width: 0
            layer.enabled: true
            layer.effect: DropShadow {
                radius: 4
                samples: 9
                color: "#8f8f8f"
                horizontalOffset: 2
                verticalOffset: 2
            }
        }

        // 前节
        Shortcut {
            sequence: "N"
            enabled: networkMenu.opened
            onActivated: {
                if (networkMenu.opened) {
                    addHeadNetWork()
                    networkMenu.close()
                }
            }
        }

        // 后节
        Shortcut {
            sequence: "W"
            enabled: networkMenu.opened
            onActivated: {
                if (networkMenu.opened) {
                    addTailNetWork()
                    networkMenu.close()
                }
            }
        }

        // 串联触点(后)
        Shortcut {
            sequence: "E"
            enabled: networkMenu.opened
            onActivated: {
                if (networkMenu.opened) {
                    addSeriesContact(startBlockNumber, 2)
                    ldManage.fixLDFileConnections(fileKey)
                    networkMenu.close()
                }
            }
        }

        // 复制
        Shortcut {
            sequence: "C"
            enabled: !blockMenu.opened && networkMenu.opened
            onActivated: {
                if (networkMenu.opened) {
                    shortcutController.addContentToClipboard(multiShortcutHandler.copy())
                    getNetWork()
                    networkMenu.close()
                }
            }
        }

        // 剪切
        Shortcut {
            sequence: "T"
            enabled: !blockMenu.opened && networkMenu.opened && netWorkList.length > 1
            onActivated: {
                if (networkMenu.opened) {
                    shortcutController.addContentToClipboard(multiShortcutHandler.shear())
                    getNetWork(false)
                    networkMenu.close()
                }
            }
        }

        // 粘贴
        Shortcut {
            sequence: "V"
            enabled: !blockMenu.opened && networkMenu.opened
            onActivated: {
                if (networkMenu.opened) {
                    multiShortcutHandler.paste_(0, shortcutController.getClipbooardContent)
                    getNetWork()
                    networkMenu.close()
                }
            }
        }

        // 上粘贴
        Shortcut {
            sequence: "U"
            enabled: !blockMenu.opened && networkMenu.opened
            onActivated: {
                if (networkMenu.opened) {
                    multiShortcutHandler.paste_(1, shortcutController.getClipbooardContent)
                    getNetWork()
                    networkMenu.close()
                }
            }
        }

        // 删除
        Shortcut {
            sequence: "D"
            enabled: networkMenu.opened
            onActivated: {
                if (networkMenu.opened) {
                    multiShortcutHandler.del()
                    networkMenu.close()
                }
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Front Node(N)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.hovered ? "#024184" : "#545454"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                addHeadNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("After Node(W)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.hovered ? "#024184" : "#545454"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                addTailNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Series Contact(After)(E)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                addSeriesContact(startBlockNumber, 2)
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        Rectangle {
            width: parent.width
            height: 1
            color: "#6f9ccf"
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Copy(C)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+C"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                shortcutController.addContentToClipboard(multiShortcutHandler.copy())
                getNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: netWorkList.length > 1
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Shear(T)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+X"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                shortcutController.addContentToClipboard(multiShortcutHandler.shear())
                getNetWork(false)
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Paste(V)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+V"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.paste_(1, shortcutController.getClipbooardContent)
                getNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Upper Paste(U)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.paste_(0, shortcutController.getClipbooardContent)
                getNetWork()
            }
        }

        Rectangle {
            width: parent.width
            height: 1
            color: "#6f9ccf"
        }

        MenuItem {
            leftPadding: 10
            height: 25
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Delete(D)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "DEL"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.del()
            }
        }
    }

    Menu {
        id: blockMenu
        property string selectBlockType
        width: 200
        topPadding: 10
        bottomPadding: 10
        leftPadding: 10
        rightPadding: 10
        background: Rectangle {
            color: "#f2f2f2"
            border.width: 0
            layer.enabled: true
            layer.effect: DropShadow {
                radius: 4
                samples: 9
                color: "#8f8f8f"
                horizontalOffset: 2
                verticalOffset: 2
            }
        }

        // 串联触点(后)
        Shortcut {
            sequence: "E"
            enabled: blockMenu.opened
            onActivated: {
                // 串联触点(后)只有在不是结束块元件才能添加
                if (blockMenu.opened && !isFlowEndBlock(
                            currentSelectBlockType)) {
                    addSeriesContact(currentSelectBolckData.Number, 2)
                    ldManage.fixLDFileConnections(fileKey)
                    blockMenu.close()
                }
            }
        }

        // 串联触点(前)
        Shortcut {
            sequence: "F"
            enabled: blockMenu.opened
            onActivated: {
                if (blockMenu.opened) {
                    addSeriesContact(currentSelectBolckData.Number, 0)
                    ldManage.fixLDFileConnections(fileKey)
                    blockMenu.close()
                }
            }
        }

        // 并联触点
        Shortcut {
            sequence: "P"
            enabled: blockMenu.opened
            onActivated: {
                // 并联触点只有在不是结束块元件才能添加
                if (blockMenu.opened && !isFlowEndBlock(
                            currentSelectBlockType)) {
                    ldManage.addParallelContact(fileKey,
                                                currentSelectBolckData.Number)
                    ldManage.fixLDFileConnections(fileKey)
                    blockMenu.close()
                }
            }
        }

        // 线圈
        Shortcut {
            sequence: "L"
            enabled: blockMenu.opened
            onActivated: {
                // 只有当前选中的块元件是当前x轴中最大的一个才能添加线圈
                if (blockMenu.opened && currentSelectBolckDataIsMaxXPos
                        && ldManage.canAddCoilJumpReturn(
                            fileKey, netWorkNumber,
                            currentSelectBolckData.Number)) {
                    ldManage.addCoilComponent(fileKey, netWorkNumber,
                                              currentSelectBolckData.Number,
                                              "Coil")
                    ldManage.fixLDFileConnections(fileKey)
                    blockMenu.close()
                }
            }
        }

        // 跳转
        Shortcut {
            sequence: "J"
            enabled: blockMenu.opened
            onActivated: {
                // 只有当前选中的块元件是当前x轴中最大的一个才能添加跳转
                if (blockMenu.opened && currentSelectBolckDataIsMaxXPos
                        && isNotIncludeJump() && ldManage.canAddCoilJumpReturn(
                            fileKey, netWorkNumber,
                            currentSelectBolckData.Number)) {
                    ldManage.addCoilComponent(fileKey, netWorkNumber,
                                              currentSelectBolckData.Number,
                                              "Jump")
                    ldManage.fixLDFileConnections(fileKey)
                    blockMenu.close()
                }
            }
        }

        // 返回
        Shortcut {
            sequence: "R"
            enabled: blockMenu.opened && !networkMenu.opened
            onActivated: {
                // 只有当前选中的块元件是当前x轴中最大的一个才能添加返回
                if (blockMenu.opened && currentSelectBolckDataIsMaxXPos
                        && ldManage.canAddCoilJumpReturn(
                            fileKey, netWorkNumber,
                            currentSelectBolckData.Number)) {
                    ldManage.addCoilComponent(fileKey, netWorkNumber,
                                              currentSelectBolckData.Number,
                                              "Return")
                    ldManage.fixLDFileConnections(fileKey)
                    blockMenu.close()
                }
            }
        }

        // 块元件
        Shortcut {
            sequence: "B"
            enabled: blockMenu.opened
            onActivated: {
                if (blockMenu.opened) {
                    function_block_ele_select.open()
                    blockMenu.close()
                }
            }
        }

        // 置反
        Shortcut {
            sequence: "G"
            enabled: blockMenu.opened && !pinMenu.opened
            onActivated: {
                // 选中的块元件非Function、FunctionBlock、advance、set0、set1才能置反
                if (blockMenu.opened && !pinMenu.opened
                        && currentSelectBlockType !== "func"
                        && currentSelectBlockType !== "fb"
                        && currentSelectBlockType !== "advance"
                        && currentSelectBlockType !== "set0"
                        && currentSelectBlockType !== "set1") {
                    multiShortcutHandler.reverse()
                    blockMenu.close()
                }
            }
        }

        // 置位/复位
        Shortcut {
            sequence: "S"
            enabled: blockMenu.opened && isFlowEndBlock(currentSelectBlockType)
            onActivated: {
                // 选中的块元件只有在是线圈的情况下才能置位/复位
                if (blockMenu.opened
                        && (currentSelectBlockType === "coil"
                            || currentSelectBlockType === "set1"
                            || currentSelectBlockType === "set0")) {
                    multiShortcutHandler.setOrReset()
                    blockMenu.close()
                }
            }
        }

        // 复制
        Shortcut {
            sequence: "C"
            enabled: blockMenu.opened && !networkMenu.opened
            onActivated: {
                if (blockMenu.opened) {
                    copyOrCutType = currentSelectBlockType
                    shortcutController.addContentToClipboard(multiShortcutHandler.copy())
                    getNetWork()
                    blockMenu.close()
                }
            }
        }

        // 剪切
        Shortcut {
            sequence: "T"
            enabled: blockMenu.opened && !networkMenu.opened && isCanDelete()
            onActivated: {
                if (blockMenu.opened) {
                    copyOrCutType = currentSelectBlockType
                    shortcutController.addContentToClipboard(multiShortcutHandler.shear())
                    getNetWork(false)
                    blockMenu.close()
                }
            }
        }

        // 右粘贴
        Shortcut {
            sequence: "V"
            enabled: blockMenu.opened && !networkMenu.opened
            onActivated: {
                if (blockMenu.opened && isCanPaste()) {
                    multiShortcutHandler.rightPaste(shortcutController.getClipbooardContent)
                    getNetWork()
                    blockMenu.close()
                }
            }
        }

        // 下粘贴
        Shortcut {
            sequence: "O"
            enabled: blockMenu.opened && !networkMenu.opened
            onActivated: {
                if (blockMenu.opened && isCanPaste(2)) {
                    multiShortcutHandler.lowerPaste(shortcutController.getClipbooardContent)
                    getNetWork()
                    blockMenu.close()
                }
            }
        }

        // 左粘贴
        Shortcut {
            sequence: "A"
            enabled: blockMenu.opened && !networkMenu.opened
            onActivated: {
                if (blockMenu.opened && isCanPaste(0)) {
                    multiShortcutHandler.leftPaste(shortcutController.getClipbooardContent)
                    getNetWork()
                    blockMenu.close()
                }
            }
        }

        // 删除
        Shortcut {
            sequence: "D"
            enabled: blockMenu.opened
            onActivated: {
                if (blockMenu.opened && isCanDelete()) {
                    deleteBlock()
                    blockMenu.close()
                }
            }
        }

        MenuItem {
            leftPadding: 10
            height: isFlowEndBlock(currentSelectBlockType) ? 0 : 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Series Contact(After)(E)")
                visible: !isFlowEndBlock(currentSelectBlockType)
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                addSeriesContact(currentSelectBolckData.Number, 2)
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Series Contact(Front)(F)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                addSeriesContact(currentSelectBolckData.Number, 0)
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        MenuItem {
            leftPadding: 10
            height: isFlowEndBlock(currentSelectBlockType) ? 0 : 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Parallel Contact(P)")
                visible: !isFlowEndBlock(currentSelectBlockType)
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                ldManage.addParallelContact(fileKey,
                                            currentSelectBolckData.Number)
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: currentSelectBolckDataIsMaxXPos
                     && ldManage.canAddCoilJumpReturn(
                         fileKey, netWorkNumber, currentSelectBolckData.Number)
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Coil(L)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                ldManage.addCoilComponent(fileKey, netWorkNumber,
                                          currentSelectBolckData.Number, "Coil")
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: currentSelectBolckDataIsMaxXPos && isNotIncludeJump()
                     && ldManage.canAddCoilJumpReturn(
                         fileKey, netWorkNumber, currentSelectBolckData.Number)
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Jump(J)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                ldManage.addCoilComponent(fileKey, netWorkNumber,
                                          currentSelectBolckData.Number, "Jump")
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: currentSelectBolckDataIsMaxXPos
                     && ldManage.canAddCoilJumpReturn(
                         fileKey, netWorkNumber, currentSelectBolckData.Number)
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Return(R)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                ldManage.addCoilComponent(fileKey, netWorkNumber,
                                          currentSelectBolckData.Number,
                                          "Return")
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Text {
                text: qsTr("Block Element(B)")
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: config.menuFontPixeSize
                color: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"
                Behavior on x {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.InOutQuad
                    }
                }
                x: parent.hovered ? 11 : 10
            }
            onTriggered: {
                function_block_ele_select.open()
            }
        }

        Rectangle {
            width: parent.width
            height: 1
            color: "#6f9ccf"
        }

        Rectangle {
            id: childer
            height: (currentSelectBlockType === "func"
                     || currentSelectBlockType === "fb"
                     || currentSelectBlockType === "advance") ? 25 : 0
            enabled: (currentSelectBlockType === "func"
                      || currentSelectBlockType === "fb"
                      || currentSelectBlockType === "advance")
            color: hover ? "#fcfcfc" : "transparent"
            border.color: hover ? "#00e1db" : "transparent"
            border.width: hover ? 1 : 0
            property bool hover: false

            RowLayout {
                visible: (currentSelectBlockType === "func"
                          || currentSelectBlockType === "fb"
                          || currentSelectBlockType === "advance")
                anchors.fill: parent
                anchors.verticalCenter: parent.verticalCenter
                spacing: 0

                Text {
                    text: qsTr("Senior")
                    Layout.alignment: Qt.AlignVCenter
                    font.pixelSize: config.menuFontPixeSize
                    color: childer.hover ? "#024184" : "#545454"
                    Layout.minimumWidth: 105
                    Layout.maximumWidth: 105
                    Layout.leftMargin: childer.hover ? 11 : 10
                }

                Item {
                    Layout.fillWidth: true
                }

                Row {
                    Layout.alignment: Qt.AlignVCenter
                    Layout.rightMargin: childer.hover ? 5 : 4

                    Repeater {
                        model: 5
                        Rectangle {
                            width: 1
                            height: 9 - index * 2
                            anchors.verticalCenter: parent.verticalCenter
                            color: childer.hover ? "#024184" : "#545454"
                        }
                    }
                }
            }

            MouseArea {
                anchors.fill: parent
                hoverEnabled: true
                onEntered: {
                    childerTimer.stop()
                    parent.hover = true

                    childerMenu.x = blockMenu.width - 20
                    childerMenu.y = -10
                    childerMenu.open()
                }
                onExited: {
                    childerTimer.stop()
                    childerTimer.start()
                    parent.hover = false
                }
            }

            Timer {
                id: childerTimer
                interval: 150
                repeat: false
                onTriggered: {
                    parent.hover = false
                    childerMenu.close()
                }
            }

            Menu {
                id: childerMenu
                width: 200
                height: 70
                topPadding: 10
                bottomPadding: 10
                leftPadding: 10
                rightPadding: 10
                background: Rectangle {
                    color: "#f2f2f2"
                    border.width: 0
                    layer.enabled: true
                    layer.effect: DropShadow {
                        radius: 4
                        samples: 9
                        color: "#8f8f8f"
                        horizontalOffset: 2
                        verticalOffset: 2
                    }
                }

                // 多输入
                Shortcut {
                    sequence: "M"
                    enabled: blockMenu.opened && isAdvance()
                    onActivated: {
                        if (blockMenu.opened && childerMenu.opened) {
                            multiShortcutHandler.multipleInput()
                            childerMenu.close()
                            blockMenu.close()
                        }
                    }
                }

                // 多输出
                Shortcut {
                    sequence: "N"
                    enabled: blockMenu.opened && isAdvanceAndMove()
                    onActivated: {
                        if (blockMenu.opened && childerMenu.opened) {
                            multiShortcutHandler.multipleOutput()
                            childerMenu.close()
                            blockMenu.close()
                        }
                    }
                }

                MenuItem {
                    leftPadding: 10
                    height: 25
                    enabled: isAdvance()
                    background: Rectangle {
                        color: parent.hovered ? "#fcfcfc" : "transparent"
                        border.color: parent.hovered ? "#00e1db" : "transparent"
                        border.width: parent.hovered ? 1 : 0
                    }
                    contentItem: Item {
                        anchors.fill: parent
                        anchors.leftMargin: parent.hovered ? 11 : 10
                        anchors.rightMargin: parent.hovered ? 11 : 10
                        property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                        RowLayout {
                            anchors.fill: parent
                            anchors.verticalCenter: parent.verticalCenter
                            Text {
                                text: qsTr("Multiple In(M)")
                                Layout.alignment: Qt.AlignVCenter
                                font.pixelSize: config.menuFontPixeSize
                                color: parent.parent.textColor
                                Layout.minimumWidth: 105
                                Layout.maximumWidth: 105
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            Text {
                                text: "CTRL+D"
                                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                                font.pixelSize: config.menuFontPixeSize
                                color: parent.parent.textColor
                            }
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        hoverEnabled: true
                        onEntered: {
                            childerTimer.stop()
                        }
                        onExited: {
                            childerTimer.stop()
                            childerTimer.start()
                        }
                        onClicked: {
                            multiShortcutHandler.multipleInput()
                            childerMenu.close()
                            blockMenu.close()
                        }
                    }
                }

                MenuItem {
                    leftPadding: 10
                    height: 25
                    enabled: isAdvanceAndMove()
                    background: Rectangle {
                        color: parent.hovered ? "#fcfcfc" : "transparent"
                        border.color: parent.hovered ? "#00e1db" : "transparent"
                        border.width: parent.hovered ? 1 : 0
                    }
                    contentItem: Item {
                        anchors.fill: parent
                        anchors.leftMargin: parent.hovered ? 11 : 10
                        anchors.rightMargin: parent.hovered ? 11 : 10
                        property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                        RowLayout {
                            anchors.fill: parent
                            anchors.verticalCenter: parent.verticalCenter
                            Text {
                                text: qsTr("Multiple Out(N)")
                                Layout.alignment: Qt.AlignVCenter
                                font.pixelSize: config.menuFontPixeSize
                                color: parent.parent.textColor
                                Layout.minimumWidth: 105
                                Layout.maximumWidth: 105
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            Text {
                                text: "CTRL+E"
                                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                                font.pixelSize: config.menuFontPixeSize
                                color: parent.parent.textColor
                            }
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        hoverEnabled: true
                        onEntered: {
                            childerTimer.stop()
                        }
                        onExited: {
                            childerTimer.stop()
                            childerTimer.start()
                        }
                        onClicked: {
                            multiShortcutHandler.multipleOutput()
                            childerMenu.close()
                            blockMenu.close()
                        }
                    }
                }
            }
        }

        Rectangle {
            width: parent.width
            height: 1
            color: "#6f9ccf"
            visible: (currentSelectBlockType === "func"
                      || currentSelectBlockType === "fb"
                      || currentSelectBlockType === "advance")
        }

        MenuItem {
            leftPadding: 10
            height: (currentSelectBlockType !== "func"
                     && currentSelectBlockType !== "fb"
                     && currentSelectBlockType !== "advance") ? 25 : 0
            enabled: currentSelectBlockType !== "set0"
                     && currentSelectBlockType !== "set1"
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                visible: (currentSelectBlockType !== "func"
                          && currentSelectBlockType !== "fb"
                          && currentSelectBlockType !== "advance")
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Reverse(G)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+G"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.reverse()
                ldManage.fixLDFileConnections(fileKey)
            }
        }

        MenuItem {
            leftPadding: 10
            height: currentSelectBlockType === "coil"
                    || currentSelectBlockType === "set1"
                    || currentSelectBlockType === "set0" ? 25 : 0
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                visible: currentSelectBlockType === "coil"
                         || currentSelectBlockType === "set1"
                         || currentSelectBlockType === "set0"
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Set/Reset(S)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+T"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.setOrReset()
            }
        }

        Rectangle {
            width: parent.width
            height: 1
            color: "#6f9ccf"
            visible: (currentSelectBlockType !== "func"
                      && currentSelectBlockType !== "fb"
                      && currentSelectBlockType !== "advance")
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: true
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Copy(C)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+C"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                copyOrCutType = currentSelectBlockType
                shortcutController.addContentToClipboard(multiShortcutHandler.copy())
                getNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: isCanDelete()
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Shear(T)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+X"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                copyOrCutType = currentSelectBlockType
                shortcutController.addContentToClipboard(multiShortcutHandler.shear())
                getNetWork(false)
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: isCanPaste()
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Right Paste(V)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+V"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.rightPaste(shortcutController.getClipbooardContent)
                getNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: isCanPaste(0)
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Left Paste(A)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+A"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.leftPaste(shortcutController.getClipbooardContent)
                getNetWork()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: isCanPaste(2)
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Down Paste(O)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+O"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.lowerPaste(shortcutController.getClipbooardContent)
                getNetWork()
            }
        }

        Rectangle {
            width: parent.width
            height: 1
            color: "#6f9ccf"
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: isCanDelete()
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Delete(D)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "DEL"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                deleteBlock()
            }
        }
    }

    Menu {
        id: pinMenu
        width: 200
        topPadding: 10
        bottomPadding: 10
        leftPadding: 10
        rightPadding: 10
        background: Rectangle {
            color: "#f2f2f2"
            border.width: 0
            layer.enabled: true
            layer.effect: DropShadow {
                radius: 4
                samples: 9
                color: "#8f8f8f"
                horizontalOffset: 2
                verticalOffset: 2
            }
        }

        // 多输入
        Shortcut {
            sequence: "M"
            enabled: pinMenu.opened && isAdvance()
            onActivated: {
                if (pinMenu.opened && currentSelectBlockType !== "output"
                        && currentSelectBlockType !== "eno") {
                    multiShortcutHandler.multipleInput()
                    pinMenu.close()
                }
            }
        }

        // 多输出
        Shortcut {
            sequence: "N"
            enabled: pinMenu.opened && isAdvanceAndMove()
            onActivated: {
                if (pinMenu.opened && currentSelectBlockType !== "input"
                        && currentSelectBlockType !== "en") {
                    multiShortcutHandler.multipleOutput()
                    pinMenu.close()
                }
            }
        }

        // 置反
        Shortcut {
            sequence: "G"
            enabled: pinMenu.opened && !blockMenu.opened
            onActivated: {
                if (pinMenu.opened && !blockMenu.opened
                        && currentSelectBolckData.DataType.toLowerCase(
                            ) === "bool") {
                    multiShortcutHandler.reverse()
                    pinMenu.close()
                }
            }
        }

        // 删除
        Shortcut {
            sequence: "D"
            enabled: pinMenu.opened && isAdvance()
            onActivated: {
                if (pinMenu.opened
                        && (currentSelectBlockType === "input"
                            || currentSelectBlockType === "output")) {
                    multiShortcutHandler.del()
                    pinMenu.close()
                }
            }
        }

        MenuItem {
            leftPadding: 10
            height: (currentSelectBlockType === "eno"
                     || currentSelectBlockType === "output") ? 0 : 25
            enabled: isAdvance()
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                visible: !(currentSelectBlockType === "eno"
                           || currentSelectBlockType === "output")
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Multiple In(M)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+D"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.multipleInput()
            }
        }

        MenuItem {
            leftPadding: 10 // 只有move块元件才能多输出
            height: (currentSelectBlockType === "en"
                     || currentSelectBlockType === "input") ? 0 : 25
            enabled: isAdvanceAndMove()
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                visible: !(currentSelectBlockType === "en"
                           || currentSelectBlockType === "input")
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Multiple Out(N)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+E"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.multipleOutput()
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25
            enabled: isEnable()
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Reverse(G)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "CTRL+G"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.reverse()
            }

            function isEnable() {
                if (currentSelectBlockType
                        && (currentSelectBlockType === "en"
                            || currentSelectBlockType === "eno"
                            || currentSelectBlockType === "input"
                            || currentSelectBlockType === "output")
                        && currentSelectBolckData.DataType
                        && currentSelectBolckData.DataType.toLowerCase(
                            ) === "bool") {
                    return true
                }

                return false
            }
        }

        MenuItem {
            leftPadding: 10
            height: 25 // (currentSelectBlockType === "eno" || currentSelectBlockType === "output") ? 0 : 25
            enabled: (currentSelectBlockType !== "en" && currentSelectBlockType
                      !== "eno" && currentSelectBlockType !== "output" && isAdvance(
                          )) || (currentSelectBlockType !== "en" && currentSelectBlockType
                                 !== "eno" && currentSelectBlockType
                                 !== "input" && isAdvanceAndMove())
            background: Rectangle {
                color: parent.hovered ? "#fcfcfc" : "transparent"
                border.color: parent.hovered ? "#00e1db" : "transparent"
                border.width: parent.hovered ? 1 : 0
            }
            contentItem: Item {
                // visible: !(currentSelectBlockType === "eno" || currentSelectBlockType === "output")
                anchors.fill: parent
                anchors.leftMargin: parent.hovered ? 11 : 10
                anchors.rightMargin: parent.hovered ? 11 : 10
                property color textColor: parent.enabled ? (parent.hovered ? "#024184" : "#545454") : "#9b9b9b"

                RowLayout {
                    anchors.fill: parent
                    anchors.verticalCenter: parent.verticalCenter
                    Text {
                        text: qsTr("Delete(D)")
                        Layout.alignment: Qt.AlignVCenter
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                        Layout.minimumWidth: 105
                        Layout.maximumWidth: 105
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "DEL"
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                        font.pixelSize: config.menuFontPixeSize
                        color: parent.parent.textColor
                    }
                }
            }
            onTriggered: {
                multiShortcutHandler.del()
            }
        }
    }

    // 块元件选择窗口
    FunctionBlockEleSelect {
        id: function_block_ele_select
        width: 400
        height: 450
        x: (root.width - width) * 0.4
        modal: true
        focus: true
        closePolicy: Popup.NoAutoClose
        deviceName: control.deviceName
        owned: control.owned
        type: control.type
    }

    // 右键菜单显示
    function showMenu(x, y, menuType, selectBlockType = "", selectBlockData = "", isMaxXPos = false) {
        currentOpenMenu = menuType
        currentSelectBolckData = selectBlockData
        currentSelectBlockType = selectBlockType
        currentSelectBolckDataIsMaxXPos = isMaxXPos
        
        switch (menuType) {
        case "NETWORK":
            if (y + networkMenu.height > height) {
                y = height - networkMenu.height
            }

            networkMenu.x = x
            networkMenu.y = y
            networkMenu.open()
            break
        case "BLOCK":
            if (y + blockMenu.height > height) {
                y = height - blockMenu.height
            }

            blockMenu.x = x
            blockMenu.y = y
            blockMenu.open()
            break
        case "EDITOR":
            if (y + editorMenu.height > height) {
                y = height - editorMenu.height
            }

            editorMenu.x = x
            editorMenu.y = y
            editorMenu.open()
            break
        case "PIN":
            if (y + pinMenu.height > height) {
                y = height - pinMenu.height
            }

            pinMenu.x = x
            pinMenu.y = y
            pinMenu.open()
            break
        }
    }

    // 获取网络
    function getNetWork(clearShort = true) {
        if(clearShort)
        {
            // 清除剪切标识
            shortcutController.clearShearContent()
        }
        // 获取网络列表以及网络下的所有块元件信息
        const currentNetWorkList = ldManage.getAllInfo(fileKey)
        // 解析网络数据
        analysisNetWorkData(populateNetWorkConnections(currentNetWorkList))
        // 保存本次操作的网络数据
        multiShortcutHandler.addSnapshot(JSON.parse(JSON.stringify(currentNetWorkList)))
    }

    // 解析网络数据
    function analysisNetWorkData(currentNetWorkList) {
        for (var nIndex = 0; nIndex < currentNetWorkList.length; nIndex++) {
            // 变量块元件
            const variableBlocks = []
            // 网络下所有的块元件
            const components = currentNetWorkList[nIndex].components
            // 网络下所有的连接
            const connections = currentNetWorkList[nIndex].connections

            // 查找置反的引脚以及引脚id
            for (var cIndex = 0; cIndex < components.length; cIndex++) {
                for (var _cIndex = 0; _cIndex < components.length; _cIndex++) {
                    const _component = components[_cIndex]

                    if (_component.ParentNumber === components[cIndex].Number) {
                        for (var connIndex = 0; connIndex < connections.length; connIndex++) {
                            if (connections[connIndex].TargetComponentNumber === _component.ParentNumber
                                    && connections[connIndex].TargetDataType.toLowerCase(
                                        ) === "bool") {
                                components[cIndex]["PinId"] = connections[connIndex].TargetPinId

                                const connectors = components[cIndex].connectors

                                for (var connectorIndex = 0; connectorIndex
                                     < connectors.length; connectorIndex++) {
                                    if (components[cIndex]["PinId"]
                                            === connectors[connectorIndex].PinId) {
                                        components[cIndex]["Negated"]
                                                = connectors[connectorIndex].Negated
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 过滤掉start以及不需要显示的块元件
            for (cIndex = 0; cIndex < components.length; ) {
                // 块元件数据
                const blockData = components[cIndex]

                switch (getBlockType(blockData)) {
                case "variable":
                    // 变量块元件
                    variableBlocks.push(components[cIndex])
                    components.splice(cIndex, 1)
                    // components[cIndex].Width = 0
                    // components[cIndex].Height = 0
                    // cIndex++
                    break
                case "start":
                    // start块元件
                    // 如果不需要显示start块元件则需要将start块元件的宽度设置为0
                    // 不能将高度也设置为0,如果高度为0的情况下将找不到start块元件的中心坐标轴
                    components[cIndex].Width = 0
                    components[cIndex].Height = config.defaultBlockHeight
                    cIndex++
                    break
                case "func":
                case "fb":
                case "advance":
                    // Function、FunctionBlock、advance块元件
                    components[cIndex].Width = config.defaultBlockWidth * 3
                    // 计算出的引脚高度加上一个默认的AND块高度
                    components[cIndex].Height = getPinHeight(
                                components[cIndex]) + 1
                    cIndex++
                    break
                default:
                    // 其余块元件的高宽度按照默认的高宽来
                    components[cIndex].Width = config.defaultBlockWidth
                    components[cIndex].Height = config.defaultBlockHeight
                    cIndex++
                    break
                }
            }

            components.sort((a, b) => {
                                if (a.XPos !== b.XPos) {
                                    return a.XPos - b.XPos
                                }
                                return a.XPos - b.XPos
                            })

            currentNetWorkList[nIndex]["variableBlocks"] = variableBlocks
            currentNetWorkList[nIndex]["components"] = components
        }
        
        netWorkList = currentNetWorkList
    }

    // 为每个网络填充链接
    function populateNetWorkConnections(currentNetWorkList) {
        for (var nIndex = 0; nIndex < currentNetWorkList.length; nIndex++) {
            currentNetWorkList[nIndex].connections = ldManage.getNetworkConnections(
                        fileKey, currentNetWorkList[nIndex].Number)
        }

        currentNetWorkList.copyOrCutType = copyOrCutType

        return currentNetWorkList
    }

    // 获取块元件类型
    function getBlockType(blockData) {
        // 块元件类型
        let blockType = ""
        // 元件类型
        const type = blockData.Type.toLowerCase()
        // 子类型
        const childType = blockData.ChildType.toLowerCase()
        // 辅助文档
        const auxContent = blockData.AuxContent.toLowerCase()
        // 辅助信息
        const auxContent1 = blockData.AuxContent1.toLowerCase()

        if (type === "variable" && childType === "constant"
                   && auxContent1 === "start") {
            // start元件
            blockType = "start"
        }
        else if (type === "variable" || (childType === "local"
                                    || childType === "???")
                // && (auxContent === "1" || auxContent === "???")
                ) {
            // variable元件
            blockType = "variable"
        } else {
            blockType = auxContent
        }

        return blockType
    }

    // 查找当前网络中是否未包含jump块元件
    function isNotIncludeJump() {
        for (var nIndex = 0; nIndex < netWorkList.length; nIndex++) {
            const netWork = netWorkList[nIndex]

            if (netWork.Number === netWorkNumber) {
                // 当前所在的网络下所有的块元件
                const components = netWork.components

                for (var cIndex = 0; cIndex < components.length; cIndex++) {
                    const component = components[cIndex]

                    if (getBlockType(component) === "jump") {
                        return false
                    }
                }
            }
        }

        return true
    }

    // 快元件是否可以删除
    function isCanDelete() {
        // 如果是线圈并且是当前网络中最后的一个线圈
        // 那该线圈是不可以被删除的
        if (currentSelectBlockType && netWorkNumber <= 0
                && (currentSelectBlockType === "coil"
                    || currentSelectBlockType === "set0"
                    || currentSelectBlockType === "set1")) {
            // 校验是否是当前网络中最后一个线圈
            for (var nIndex = 0; nIndex < netWorkList.length; nIndex++) {
                const netWork = netWorkList[nIndex]
                // 查找当前所在的网络
                if (netWork.Number === netWorkNumber) {
                    // 网络下所有的块元件
                    const components = netWork.components
                    // 线圈数量
                    let coilCount = 0

                    for (var cIndex = 0; cIndex < components.length; cIndex++) {
                        // 快元件类型
                        const componentType = getBlockType(components[cIndex])

                        if (componentType === "coil" || componentType === "set0"
                                || componentType === "set1") {
                            coilCount++
                        }
                    }
                    return coilCount > 1
                }
            }
        }

        return true
    }

    // 是否可以粘贴 location:0左粘贴 1右粘贴 2下粘贴
    function isCanPaste(location = 1) {
        if(!copyOrCutType || !currentSelectBlockType || 
           !currentSelectBolckData || netWorkNumber < 0)
        {
            return false
        }
        
        // 触点
        if (!isFlowEndBlock(copyOrCutType)
                && !(copyOrCutType === "func" || copyOrCutType === "fb"
                     || copyOrCutType === "advance")) {
            if (location === 0) {
                return true
            } else if (location === 1) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")) {
                    // 选中的块元件也是触点
                    return true
                } else if (currentSelectBlockType === "func"
                           || currentSelectBlockType === "fb"
                           || currentSelectBlockType === "advance") {
                    // 选中的是扩展块元件
                    return true
                } else if (isFlowEndBlock(currentSelectBlockType)) {
                    // 选中的是线圈、跳转、返回
                    return false
                }
            } else if (location === 2) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")) {
                    // 选中的块元件也是触点
                    return true
                } else if (currentSelectBlockType === "func"
                           || currentSelectBlockType === "fb"
                           || currentSelectBlockType === "advance") {
                    // 选中的是扩展块元件
                    return false
                } else if (isFlowEndBlock(currentSelectBlockType)) {
                    // 选中的是线圈、跳转、返回
                    return false
                }
            }
        } // 扩展块元件
        else if (copyOrCutType === "func" || copyOrCutType === "fb"
                 || copyOrCutType === "advance") {
            if (location === 0) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")) {
                    // 复制和剪切的是扩展块元件
                    // 选中的块元件是触点
                    return true
                } else if (currentSelectBlockType === "func"
                           || currentSelectBlockType === "fb"
                           || currentSelectBlockType === "advance") {
                    // 选中的是扩展块元件
                    return true
                } else if (isFlowEndBlock(currentSelectBlockType)) {
                    // 选中的是线圈、跳转、返回
                    return true
                }
            } else if (location === 1) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")) {
                    // 选中的块元件是触点
                    return true
                } else if (currentSelectBlockType === "func"
                           || currentSelectBlockType === "fb"
                           || currentSelectBlockType === "advance") {
                    // 选中的是扩展块元件
                    return true
                } else if (isFlowEndBlock(currentSelectBlockType)) {
                    // 选中的是线圈、跳转、返回
                    return false
                }
            } else if (location === 2) {
                return false
            }
        } // 线圈、跳转、返回
        else if (isFlowEndBlock(copyOrCutType)) {
            if (location === 0) {
                return false
            } else if (location === 1) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")
                        && ldManage.canAddCoilJumpReturn(
                            fileKey, netWorkNumber,
                            currentSelectBolckData.Number)) {
                    // 选中的块元件是触点
                    return true
                } else if ((currentSelectBlockType === "func"
                            || currentSelectBlockType === "fb"
                            || currentSelectBlockType === "advance")
                           && ldManage.canAddCoilJumpReturn(
                               fileKey, netWorkNumber,
                               currentSelectBolckData.Number)) {
                    // 选中的是扩展块元件
                    return true
                } else if (isFlowEndBlock(currentSelectBlockType)) {
                    // 选中的是线圈、跳转、返回
                    return false
                }
            } else if (location === 2) {
                if (isFlowEndBlock(currentSelectBlockType)) {
                    // 选中的是线圈、跳转、返回
                    return true
                }
            }
        }

        return false
    }

    // 粘贴 location:0左粘贴 1右粘贴 2下粘贴
    function paste(location = 1, contentList) {
        let result = false

        if(!copyOrCutType || !currentSelectBlockType || 
           !currentSelectBolckData || netWorkNumber < 0)
        {
            return result
        }

        // 触点
        if (!isFlowEndBlock(copyOrCutType)
                && !(copyOrCutType === "func" || copyOrCutType === "fb"
                     || copyOrCutType === "advance")) {
            if (location === 0) {
                result = ldManage.pasteSeriesContact(
                            fileKey, currentSelectBolckData.Number, contentList, 0)
            } else if (location === 1) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")) {
                    // 选中的块元件也是触点
                    result = ldManage.pasteSeriesContact(
                                fileKey, currentSelectBolckData.Number, contentList, 2)
                } else if (currentSelectBlockType === "func"
                           || currentSelectBlockType === "fb"
                           || currentSelectBlockType === "advance") {
                    // 选中的是扩展块元件
                    result = ldManage.pasteSeriesContact(
                                fileKey, currentSelectBolckData.Number, contentList, 2)
                }
            } else if (location === 2) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")) {
                    // 选中的块元件也是触点
                    result = ldManage.pasteParallelContact(
                                fileKey, currentSelectBolckData.Number, contentList)
                }
            }
        } // 扩展块元件
        else if (copyOrCutType === "func" || copyOrCutType === "fb"
                 || copyOrCutType === "advance") {
            if (location === 0) {
                result = ldManage.pasteBlockComponent(
                            fileKey, currentSelectBolckData.Number, contentList, 0)
            } else if (location === 1) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")) {
                    // 选中的块元件是触点
                    result = ldManage.pasteBlockComponent(
                                fileKey, currentSelectBolckData.Number, contentList, 2)
                } else if (currentSelectBlockType === "func"
                           || currentSelectBlockType === "fb"
                           || currentSelectBlockType === "advance") {
                    // 选中的是扩展块元件
                    result = ldManage.pasteBlockComponent(
                                fileKey, currentSelectBolckData.Number, contentList, 2)
                }
            } else if (location === 2) {
                return
            }
        } // 线圈、跳转、返回
        else if (isFlowEndBlock(copyOrCutType)) {
            if (location === 1) {
                if (!isFlowEndBlock(currentSelectBlockType)
                        && !(currentSelectBlockType === "func"
                             || currentSelectBlockType === "fb"
                             || currentSelectBlockType === "advance")
                        && ldManage.canAddCoilJumpReturn(
                            fileKey, netWorkNumber,
                            currentSelectBolckData.Number, contentList)) {
                    // 选中的块元件是触点
                    result = ldManage.pasteCoilJumpReturn(
                                fileKey, currentSelectBolckData.Number, contentList)
                } else if ((currentSelectBlockType === "func"
                            || currentSelectBlockType === "fb"
                            || currentSelectBlockType === "advance")
                           && ldManage.canAddCoilJumpReturn(
                               fileKey, netWorkNumber,
                               currentSelectBolckData.Number, contentList)) {
                    // 选中的是扩展块元件
                    result = ldManage.pasteCoilJumpReturn(
                                fileKey, currentSelectBolckData.Number, contentList)
                }
            } else if (location === 2) {
                if (isFlowEndBlock(currentSelectBlockType)) {
                    // 选中的是线圈、跳转、返回
                    result = ldManage.pasteCoilJumpReturn(
                                fileKey, currentSelectBolckData.Number, contentList)
                }
            }
        }

        return result
    }

    // 是否是高级扩展块元件
    function isAdvance() {
        if (!currentSelectBolckData || Object.keys(
                    currentSelectBolckData).length === 0
                || !currentSelectBlockType) {
            return false
        }

        if (currentSelectBlockType === "en" || currentSelectBlockType === "eno"
                || currentSelectBlockType === "input"
                || currentSelectBlockType === "output") {
            // 需要查找引脚所在的块元件是否是高级扩展块元件
            // 引脚的父id
            const parentId = currentSelectBolckData.ParentId

            for (var nIndex = 0; nIndex < netWorkList.length; nIndex++) {
                const netWork = netWorkList[nIndex]

                if (netWork.Number === netWorkNumber) {
                    // 当前网络中所有的块元件
                    const components = netWork.components

                    for (var cIndex = 0; cIndex < components.length; cIndex++) {
                        const component = components[cIndex]

                        if (component.Number === parentId) {
                            if (getBlockType(component) === "advance") {
                                return true
                            }
                        }
                    }
                }
            }
        } else if (currentSelectBlockType === "advance") {
            return true
        }

        return false
    }

    // 是否是高级块元件并且是move类型
    function isAdvanceAndMove() {
        if (!currentSelectBolckData || Object.keys(
                    currentSelectBolckData).length === 0
                || !currentSelectBlockType) {
            return false
        }

        if (currentSelectBlockType === "eno"
                || currentSelectBlockType === "output") {
            // 需要查找引脚所在的块元件是否是高级扩展块元件
            // 引脚的父id
            const parentId = currentSelectBolckData.ParentId

            for (var nIndex = 0; nIndex < netWorkList.length; nIndex++) {
                const netWork = netWorkList[nIndex]

                if (netWork.Number === netWorkNumber) {
                    // 当前网络中所有的块元件
                    const components = netWork.components

                    for (var cIndex = 0; cIndex < components.length; cIndex++) {
                        const component = components[cIndex]

                        if (component.Number === parentId) {
                            if (getBlockType(component) === "advance"
                                    && component.Name.toLowerCase(
                                        ) === "move") {
                                return true
                            }
                        }
                    }
                }
            }
        } else if (currentSelectBlockType === "advance"
                   && currentSelectBolckData.Name.toLowerCase() === "move") {
            return true
        }

        return false
    }

    // 校验块元件类型是否是流程结束块元件
    function isFlowEndBlock(blockType) {
        return ["coil", "jump", "return", "set1", "set0"].includes(blockType)
    }

    // 添加头节网络
    function addHeadNetWork() {
        if (netWorkNumber === -1) {
            // 最小的网络序号
            const minNumber = Math.min(0,
                                       ...netWorkList.map(item => item.Number))
            ldManage.addNetworkBefore(fileKey, minNumber)
        } else {
            ldManage.addNetworkBefore(fileKey, netWorkNumber)
        }
        ldManage.fixLDFileConnections(fileKey)
        getNetWork()
        updateFileFlag(fileKey, true)
    }

    // 添加尾节网络
    function addTailNetWork() {
        if (netWorkNumber === -1) {
            // 最大的网络序号
            const maxNumber = Math.max(0,
                                       ...netWorkList.map(item => item.Number))
            ldManage.addNetworkAfter(fileKey, maxNumber)
        } else {
            ldManage.addNetworkAfter(fileKey, netWorkNumber)
        }
        ldManage.fixLDFileConnections(fileKey)
        getNetWork()
        updateFileFlag(fileKey, true)
    }

    // 添加串联触点
    function addSeriesContact(blockNumber, leftOrRight) {
        if (blockNumber !== -1) {
            ldManage.addSeriesContact(fileKey, blockNumber, leftOrRight)
        }
    }

    // 添加块元件
    function addBlockComponent(blockNumber, funcBlockData) {
        if (blockNumber !== -1) {
            const funcBlocks = serviceInterface.getAllFunctionAndBlock(
                                 control.deviceName)
            // 块元件类型
            const funcBlockType = funcBlockData.Type.toLowerCase()
            // 块元件名称
            const funcBlockName = funcBlockData.Name
            // 块元件默认数据
            const blockData = {
                "InstanceName": funcBlockName,
                "ChildType": funcBlockData.Type,
                "AuxContent": funcBlockData.Type,
                "InputPinNum": 0,
                "OutputPinNum": 0,
                "SupportsInPinType": "",
                "SupportsOutPinType": "",
                "XPos": 0,
                "YPos": 0,
                "Width": 15,
                "Height": 5,
                "connectors": []
            }

            // 指定类型的所有数据
            let allDatas = []
            // 输入引脚
            let inputs = []
            // 输出引脚
            let outputs = []
            // 引脚索引编号
            let pinIndex = 1
            // 当前块元件的所有引脚数据
            let pins = []

            // 获取指定类型的数据
            if (funcBlockType === "func") {
                allDatas = funcBlocks["firmfunction"]
            } else if (funcBlockType === "fb") {
                allDatas = funcBlocks["firmfunctionblock"]
            } else if (funcBlockType === "advance") {
                allDatas = funcBlocks["advance"]
            }

            // 查找需要添加的块元件对应的输入输出引脚
            for (var aIndex = 0; aIndex < allDatas.length; aIndex++) {
                const data = allDatas[aIndex]

                if (data.name === funcBlockName) {
                    inputs = data.inputs
                    outputs = data.outputs
                }
            }
            
            blockData.InputPinNum = inputs.length
            blockData.OutputPinNum = outputs.length

            // 添加输入引脚信息
            for (var iIndex = 0; iIndex < inputs.length; iIndex++) {
                const inPin = inputs[iIndex]
                const inObj = {
                    "Name": inPin.name,
                    "DataType": inPin.type,
                    "Value": inPin.defaultValue,
                    "InitValue": inPin.initValue,
                    "PinId": pinIndex,
                    "Direction": "Input"
                }

                pins.push(inObj)
                pinIndex++
            }
            
            // 添加输出引脚信息
            for (var oIndex = 0; oIndex < outputs.length; oIndex++) {
                const outPin = outputs[oIndex]
                const outObj = {
                    "Name": outPin.name,
                    "DataType": outPin.type,
                    "Value": outPin.defaultValue,
                    "InitValue": outPin.initValue,
                    "PinId": pinIndex,
                    "Direction": "Output"
                }

                pins.push(outObj)
                pinIndex++
            }

            blockData["connectors"] = pins

            if (funcBlockType === "advance") {
                if (funcBlockName === "REDUCE_FUNC_LIMIT") {
                    blockData.Height = 7
                } else if (funcBlockName === "TYPECAST_FUNC") {
                    blockData.Height = 5
                } else if (funcBlockName === "REDUCE_FUNC_MOVE") {
                    blockData.Height = 5
                } else {
                    blockData.Height = 6
                }
            } else {
                blockData.Height = (inputs.length
                                    > outputs.length ? inputs.length : outputs.length) + 4
            }
            
            // 常规块元件
            ldManage.addBlockComponent(fileKey, netWorkNumber, blockNumber,
                                       blockData,
                                       (isFlowEndBlock(
                                            currentSelectBlockType) ? 0 : 2))
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 删除块元件
    function deleteBlock() {
        console.log("delete", currentSelectBlockType, "type block")
        console.log("delete block number", currentSelectBolckData.Number)
        switch (currentSelectBlockType) {
        case "conatct":
            ldManage.deleteContact(fileKey, currentSelectBolckData.Number)
            break
        case "set0":
        case "set1":
        case "coil":
            ldManage.deleteCoilComponent(fileKey, netWorkNumber,
                                         currentSelectBolckData.Number)
            break
        case "jump":
            ldManage.deleteCoilComponent(fileKey, netWorkNumber,
                                         currentSelectBolckData.Number)
            break
        case "return":
            ldManage.deleteCoilComponent(fileKey, netWorkNumber,
                                         currentSelectBolckData.Number)
            break
        case "func":
        case "fb":
        case "advance":
            ldManage.deleteBlockComponent(fileKey, netWorkNumber,
                                          currentSelectBolckData.Number)
            break
        }

        ldManage.fixLDFileConnections(fileKey)
    }

    // 缩放
    function scale(event) {
        event.accepted = true
        if (event.modifiers & Qt.ControlModifier) {
            // 缩放
            const delta = event.angleDelta.y / 120
            const step = 0.1
            scaleFactor = parseFloat(
                        Math.max(
                            0.5, Math.min(
                                5.0,
                                (scaleFactor / 100) + delta * step)).toFixed(
                            2)) * 100
        } else {
            // 滚动幅度
            const scrollFactor = 0.2
            // 计算出Flickable新的滚动位置
            let newY = flick.contentY - event.angleDelta.y * scrollFactor
            let newX = flick.contentX - event.angleDelta.x * scrollFactor
            // 防止超出边界
            newY = Math.max(0,
                            Math.min(flick.contentHeight - flick.height, newY))
            newX = Math.max(0, Math.min(flick.contentWidth - flick.width, newX))
            flick.contentY = newY
            flick.contentX = newX
        }
    }

    // 获取Function、FunctionBlock、Advance块元件的引脚高度
    function getPinHeight(component) {
        let leftCount = 0
        let rightCount = 0
        const connectors = component.connectors

        for (var cIndex = 0; cIndex < connectors.length; cIndex++) {
            const direction = connectors[cIndex].Direction.toLowerCase()
            const name = connectors[cIndex].Name.toLowerCase()

            if (direction === "input" || name === "en") {
                leftCount++
            } else if (direction === "output" || name === "eno") {
                rightCount++
            }
        }

        const maxCount = Math.max(leftCount, rightCount)

        // + 1 + Math.floor(maxCount / 10)让块元件引脚多出空余高度
        return maxCount + 1 + Math.floor(maxCount / 10)
    }

    // 块元件是否是Function、FunctionBlock、Advance
    function isFuncOrFuncBlock(component) {
        // 元件类型
        const type = getBlockType(component)

        if (type === "func" || type === "fb" || type === "advance") {
            return true
        }

        return false
    }

    //显示选择变量面板
    function showSelectVariableDialog(data) {
        var c_selectVariableDialog = Qt.createComponent(
                    "qrc:/qml/variableshow/VariableSearchPanel.qml")
        if (c_selectVariableDialog.status === Component.Ready) {
            // console.log("showSelectVariableDialog data", JSON.stringify(data))
            control.showPopupDialog(c_selectVariableDialog)
            popupDialog.loadercenter.item.deviceName = deviceName
            //popupDialog.loadercenter.item.fileName = planInfo.Name + "." + planInfo.Code
            popupDialog.loadercenter.item.checkAddress = true
            popupDialog.loadercenter.item.checkValue = true
            popupDialog.loadercenter.item.checkInOut = (data.LeftOrRight === 0 ? "I" : "O")
            //带入原有名称
            popupDialog.loadercenter.item.defaultName
                    = (data.AuxContent === "???" ? "" : data.AuxContent)
            //带入目标数据类型
            popupDialog.loadercenter.item.defaultDataType = data.DataType

            popupDialog.loadercenter.item.searchowned
                    = ["Global.POE", "IOM.POE", planInfo.Name + "." + planInfo.Code]
            popupDialog.loadercenter.item.searchType = ["Global", "IO", "M", planInfo.Type]
            if (planInfo.Type === "PROGRAM") {
                popupDialog.loadercenter.item.searchscope = ["Global", "Local"]
            } else if (planInfo.Type === "FUNCTIONBLOCK") {
                popupDialog.loadercenter.item.searchscope
                        = ["Local", "Input", "Output", "InOut", "Static"]
            } else {
                //FUNCTION
                popupDialog.loadercenter.item.searchscope = ["Local", "Input", "Output", "InOut"]
            }

            popupDialog.loadercenter.item.selectVariableOk.connect(
                        function (vardata) {
                            console.log("selectVariableOk", control.fileKey,
                                        data.NetworkNumber, data.VarCompNumber,
                                        vardata.datatype, data.DataType)
                            //判断地址 变量 和 常量
                            if (vardata.scope === "Constant") {
                                if (ldManage.modifyVariableComponent(
                                            control.fileKey,
                                            data.NetworkNumber,
                                            data.VarCompNumber, vardata.scope,
                                            vardata.name, vardata.owned)) {
                                    popupDialog.close()
                                }
                            } else if (vardata.scope === "Address") {
                                if (ldManage.modifyVariableComponent(
                                            control.fileKey,
                                            data.NetworkNumber,
                                            data.VarCompNumber, vardata.scope,
                                            vardata.name, vardata.owned)) {
                                    popupDialog.close()
                                }
                            } else {
                                if (vardata.datatype === data.DataType) {
                                    console.log("selectVariableOk",
                                                JSON.stringify(vardata))
                                    if (ldManage.modifyVariableComponent(
                                                control.fileKey,
                                                data.NetworkNumber,
                                                data.VarCompNumber,
                                                vardata.scope, vardata.name,
                                                vardata.owned)) {
                                        popupDialog.close()
                                    }
                                }
                            }

                            getNetWork()
                            updateFileFlag(fileKey, true)
                        })
            popupDialog.loadercenter.item.clearVariable.connect(function () {
                console.log("clearVariable", control.fileKey,
                            data.NetworkNumber, data.VarCompNumber)
                if (ldManage.clearVariableComponent(control.fileKey,
                                                     data.NetworkNumber,
                                                     data.VarCompNumber)) {
                    popupDialog.close()
                }
            })

            popupDialog.loadercenter.item.selectVariableCancel.connect(
                        function () {
                            console.log("popupDialog.close()")
                            popupDialog.close()
                        })

            popupDialog.loadercenter.item.getBindData()
        }
    }

    //弹框显示
    function showPopupDialog(raiseItem) {
        popupDialog.raiseItem = raiseItem
        popupDialog.open()
    }

    QkPopupDialog {
        id: popupDialog
    }
}
