import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/qml/gv"

Rectangle {
    id: control
    // 主控件
    property var mainControl
    // 当前所在的网络组件
    property var netWorkControl
    // 配置
    property var config: LDEditorConfiger
    // 块数据
    property var blockData
    // 记录当前选中的网络中对应的start块元件序号
    property int startBlockNumber
    // 当前所在的网络号
    property int netWorkNumber
    // 父loader组件焦点
    property bool parentFocus: parent.focus
    // 输入引脚
    property var inputs: []
    // 输出引脚
    property var outputs: []
    // 是否使能
    property bool isEnable: false
    // EN引脚数据
    property var en
    // ENO引脚数据
    property var eno

    width: blockData.Width * config.cellWidth
    height: getRealityHeight(blockData)
    color: "transparent"

    onBlockDataChanged: {
        funTimer.stop()
        funTimer.start()
    }

    // 使用定时器进行防抖,防止事件被多次触发
    Timer {
        id: funTimer
        interval: 10
        repeat: false
        onTriggered: {
            // 块元件中所有的引脚数据
            const connectors = blockData.connectors
            let ins = []
            let outs = []
            let _en = {}
            let _eno = {}
            
            for(let connIndex = 0; connIndex < connectors.length; connIndex++)
            {
                // 引脚数据
                const connector = connectors[connIndex]
                connector.PinData = getPinVariableData(connector.PinId)
                // 设置引脚所在的块元件id
                connector.ParentId = blockData.Number
                
                if(connector.Name.toLowerCase() === "en")
                {
                    _en = connector
                }
                else if(connector.Name.toLowerCase() === "eno")
                {
                    _eno = connector
                }
                else if(connector.Direction.toLowerCase() === "input")
                {
                    // 输入引脚
                    ins.push(connector)
                }
                else if(connector.Direction.toLowerCase() === "output")
                {
                    // 输出引脚
                    outs.push(connector)
                }
                
            }
            
            inputs = ins
            outputs = outs
            en = _en
            eno = _eno
            
            func_load.sourceComponent = function_block
        }
    }

    function getPinVariableData(pinId)
    {
        let comp = {}

         // 当前网络中的变量元件
        const components = networkData.variableBlocks
        
        for(let comIndex = 0; comIndex < components.length; comIndex++)
        {
            const component = components[comIndex]

            if(component.ParentNumber === blockData.Number && 
               component.Type.toLowerCase() === "variable" &&
               pinId === component.ParentPinId)
            {
                comp =  component
                break
            }
        }
        
        return comp
    }

    Loader {
        id: func_load
        anchors.fill: parent
    }

    // Function、FunctionBlock、advance块元件
    Component {
        id: function_block
        Column {
            anchors.fill: parent

            // 坐标文本
            Rectangle {
                width: parent.width
                height: posTextHeight
                color: "transparent"
                Text {
                    visible: isTest
                    text: "(" + blockData.XPos + "," + blockData.YPos + ")"
                    font.pixelSize: config.fontPixelSize
                    anchors.horizontalCenter: parent.horizontalCenter
                }
            }

            Row {
                width: parent.width
                height: parent.height - posTextHeight

                // 左边引脚
                Rectangle {
                    width: parent.width / 3
                    height: parent.height
                    color: "transparent"

                    // EN引脚
                    Rectangle {
                        height: config.defaultLineWidth
                        width: parent.width
                        color: config.defaultConnectionLineColor
                        // 从当前块元件所在的y轴开始计算
                        // funBlockTextHeight 块元件文本高度
                        // cellCentreHeight EN和ENO的中间高度
                        y: control.y + funBlockTextHeight + cellCentreHeight - config.defaultLineWidth
                    }
                    
                    // EN引脚置反
                    Rectangle {
                        width: config.defaultLineWidth * 4
                        height: config.defaultLineWidth * 4
                        radius: height / 2
                        border.width: 1
                        border.color: "#000000"
                        color: "#ffffff"
                        anchors.right: parent.right
                        y: control.y + funBlockTextHeight + cellCentreHeight - height / 2 - config.defaultLineWidth / 2
                        visible: en.Negated
                    }

                    // EN引脚选中框
                    Rectangle {
                        width: parent.width / 5
                        height: config.defaultLineWidth * 3
                        color: parentFocus && focus ? "#0b59e3" : "transparent"
                        opacity: 0.5
                        anchors.right: parent.right
                        y: control.y + funBlockTextHeight + cellCentreHeight - height / 2 - config.defaultLineWidth / 2
                        
                        MouseArea {
                            anchors.fill: parent
                            acceptedButtons: Qt.LeftButton | Qt.RightButton
                            onClicked: {
                                activeFocusItem = "PIN"
                                parent.forceActiveFocus()
                                mainControl.startBlockNumber = control.startBlockNumber
                                mainControl.netWorkNumber = control.netWorkNumber
                                if (mouse.button === Qt.RightButton)
                                {
                                    const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                    mainControl.showMenu(positionInRoot.x, positionInRoot.y, "PIN", en.Name.toLowerCase(), en)
                                }
                                else
                                {
                                    mainControl.currentSelectBolckData = en
                                    mainControl.currentSelectBlockType = en.Name.toLowerCase()
                                }
                            }
                        }
                    }
                    
                    // Input引脚
                    Repeater {
                        model: inputs
                        Rectangle {
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            width: parent.width - 5
                            height: pinHeight
                            color: "transparent"
                            y: control.y + funBlockTextHeight + cellCentreHeight + ((index + 1) * pinHeight) - height / 2 - config.defaultLineWidth / 2 + index * 2
                            
                            Row {
                                anchors.fill: parent
                                // 引脚文本框
                                Rectangle {
                                    width: parent.width - parent.width / 5
                                    height: parent.height
                                    border.width: input_pin_text.activeFocus ? 1 : config.defaultLineWidth
                                    border.color: config.defaultConnectionLineColor
                                    color: input_pin_text.activeFocus ? "#6495ed" : "transparent"

                                    TextInput {
                                        id: input_pin_text
                                        anchors.fill: parent
                                        font.pixelSize: config.fontPixelSize
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                        text: inputs[index].PinData.AuxContent
                                        color: "black"
                                        clip: true
                                        selectByMouse: true
                                        selectionColor: config.defaultSelectionColor
                                        selectedTextColor: config.defaultSelectedTextColor

                                        MouseArea {
                                            anchors.fill: parent
                                            acceptedButtons: Qt.LeftButton
                                            cursorShape: Qt.OpenHandCursor
                                            onClicked: {
                                                if (mouse.button === Qt.LeftButton || mouse.button === Qt.RightButton)
                                                {
                                                    //显示变量弹窗 含全局变量 局部变量 IO变量 常量?
                                                    var newCData = {
                                                        "NetworkNumber": netWorkNumber,
                                                        "VarCompNumber": inputs[index].PinData.Number,
                                                        "DataType": inputs[index].PinData.ParentPinDataType,
                                                        "LeftOrRight": 0,
                                                        "AuxContent": inputs[index].PinData.AuxContent
                                                    }
                                                    showSelectVariableDialog(newCData)
                                                }
                                            }
                                        }
                                    }
                                }

                                // 引脚线条
                                Rectangle {
                                    width: parent.width / 5
                                    height: config.defaultLineWidth
                                    y: parent.height / 2
                                    color: config.defaultConnectionLineColor
                                }
                            }

                            // 引脚置反
                            Rectangle {
                                width: config.defaultLineWidth * 4
                                height: config.defaultLineWidth * 4
                                radius: height / 2
                                border.width: 1
                                border.color: "#000000"
                                color: "#ffffff"
                                anchors.right: parent.right
                                y: parent.height / 2 - ((height - config.defaultLineWidth) / 2)
                                visible: inputs[index].Negated
                            }

                            // 引脚选中框
                            Rectangle {
                                width: parent.width / 5
                                height: config.defaultLineWidth * 3
                                color: parentFocus && focus ? "#0b59e3" : "transparent"
                                opacity: 0.5
                                anchors.right: parent.right
                                y: parent.height / 2 - ((height - config.defaultLineWidth) / 2)

                                MouseArea {
                                    anchors.fill: parent
                                    acceptedButtons: Qt.LeftButton | Qt.RightButton
                                    onClicked: {
                                        activeFocusItem = "PIN"
                                        parent.forceActiveFocus()
                                        mainControl.startBlockNumber = control.startBlockNumber
                                        mainControl.netWorkNumber = control.netWorkNumber
                                        if (mouse.button === Qt.RightButton)
                                        {
                                            const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                            mainControl.showMenu(positionInRoot.x, positionInRoot.y, "PIN", inputs[index].Direction.toLowerCase(), inputs[index])
                                        }
                                        else
                                        {
                                            mainControl.currentSelectBolckData = inputs[index]
                                            mainControl.currentSelectBlockType = inputs[index].Direction.toLowerCase()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 中间块元件
                Rectangle {
                    width: parent.width / 3
                    height: parent.height
                    border.color: config.defaultConnectionLineColor
                    border.width: 2
                    radius: 4
                    color: "transparent"

                    Column {
                        anchors.fill: parent
                        spacing: 0
                        Item {
                            width: parent.width
                            height: funBlockTextHeight
                            Rectangle {
                                width: parent.width - config.defaultLineWidth
                                height: parent.height
                                anchors.top: parent.top
                                anchors.topMargin: config.defaultLineWidth / 2
                                anchors.bottom: parent.bottom
                                anchors.horizontalCenter: parent.horizontalCenter
                                color: "#d7d7d7"
                                border.width: 1
                                border.color: config.defaultConnectionLineColor

                                Rectangle {
                                    height: parent.height - config.defaultLineWidth
                                    width: parent.width - 10
                                    anchors.centerIn: parent
                                    color: isShear ? "transparent" : (and_text.focus || and_content.focus) && parentFocus ? "#6495ed" : "transparent"
                                    
                                    TextInput {
                                        id: and_text
                                        text: blockData.Name
                                        // color: activeFocus ? "black" : "#e431ff"
                                        color: "#e431ff"
                                        validator: RegExpValidator {
                                            regExp: GlobalVariable.varname_regular
                                        }
                                        readOnly: true
                                        clip: true
                                        anchors.fill: parent
                                        font.pixelSize: config.menuFontPixeSize
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                        selectByMouse: true
                                        selectionColor: config.defaultSelectionColor
                                        selectedTextColor: config.defaultSelectedTextColor

                                        // onTextChanged: {
                                        //     if(blockData.Name !== text)
                                        //     {
                                        //         ldManage.modifyComponentName(fileKey, blockData.Number, text)
                                        //         updateFileFlag(fileKey, true)
                                        //     }
                                        // }

                                        MouseArea {
                                            anchors.fill: parent
                                            acceptedButtons: Qt.LeftButton | Qt.RightButton
                                            onClicked: {
                                                activeFocusItem = "BLOCK"
                                                parent.forceActiveFocus()
                                                mainControl.startBlockNumber = control.startBlockNumber
                                                mainControl.netWorkNumber = control.netWorkNumber
                                                if (mouse.button === Qt.RightButton)
                                                {
                                                    const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                                    mainControl.showMenu(positionInRoot.x, positionInRoot.y, "BLOCK", getBlockType(blockData), blockData)
                                                }
                                                else
                                                {
                                                    mainControl.currentSelectBolckData = blockData
                                                    mainControl.currentSelectBlockType = getBlockType(blockData)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        Item {
                            width: parent.width
                            height: parent.height - funBlockTextHeight
                            Rectangle {
                                id: and_content
                                width: parent.width - config.defaultLineWidth * 2
                                height: parent.height - config.defaultLineWidth
                                anchors.horizontalCenter: parent.horizontalCenter
                                color: (and_text.focus || and_content.focus) && parentFocus ? "#6495ed" : "transparent"
                                Text {
                                    text: getName("EN")
                                    font.pixelSize: config.menuFontPixeSize
                                    anchors.left: parent.left
                                    anchors.leftMargin: 3
                                    visible: !isEnable
                                }

                                Text {
                                    text: getName("ENO")
                                    font.pixelSize: config.menuFontPixeSize
                                    anchors.right: parent.right
                                    anchors.rightMargin: 3
                                    visible: !isEnable
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    acceptedButtons: Qt.LeftButton | Qt.RightButton
                                    // 长按时间为50毫秒
                                    pressAndHoldInterval: 100
                                    property bool isLongPress: false
                                    // 开始长按时的坐标
                                    property int startLongPressX: 0
                                    property int startLongPressY: 0
                                    onClicked: {
                                        activeFocusItem = "BLOCK"
                                        parent.forceActiveFocus()
                                        mainControl.startBlockNumber = control.startBlockNumber
                                        mainControl.netWorkNumber = control.netWorkNumber
                                        if (mouse.button === Qt.RightButton)
                                        {
                                            const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                            mainControl.showMenu(positionInRoot.x, positionInRoot.y, "BLOCK", getBlockType(blockData), blockData,
                                                                 includeOrMaxXPosList[blockData.YPos] === blockData.XPos)
                                        }
                                        else
                                        {
                                            mainControl.currentSelectBolckData = blockData
                                            mainControl.currentSelectBlockType = getBlockType(blockData)
                                        }
                                    }
                                    onPressAndHold:  {
                                        activeFocusItem = "BLOCK"
                                        parent.forceActiveFocus()
                                        mainControl.startBlockNumber = control.startBlockNumber
                                        mainControl.netWorkNumber = control.netWorkNumber
                                        const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                        if (mouse.button === Qt.RightButton)
                                        {
                                            mainControl.showMenu(positionInRoot.x, positionInRoot.y, "BLOCK", getBlockType(blockData), blockData,
                                                                 includeOrMaxXPosList[blockData.YPos] === blockData.XPos)
                                        }
                                        else if(mouse.button === Qt.LeftButton)
                                        {
                                            mainControl.currentSelectBlockType = getBlockType(blockData)
                                            mainControl.currentSelectBolckData = blockData
                                            // 开启了长按
                                            isLongPress = true
                                            // 当前长按的块元件id
                                            mainControl.currentSelectBolckData = blockData
                                            startLongPressX = positionInRoot.x
                                            startLongPressY = positionInRoot.y
                                        }
                                    }
                                    onReleased: {
                                        // 关闭了长按
                                        isLongPress = false
                                        mainControl.isShowPositionBlocks = false
                                        startLongPressX = 0
                                        startLongPressY = 0

                                        if(Object.keys(netWorkControl.currentSelectPositionBlock).length !== 0)
                                        {
                                            // 先执行剪切再执行粘贴将触点移动到指定位置
                                            const content = ldManage.cutComponent(fileKey, blockData.Number)
                                            const currentSelectPositionBlockNumber = netWorkControl.currentSelectPositionBlock.number
                                            let component
                                            
                                            for(let nIndex = 0; nIndex < netWorkList.length; nIndex++)
                                            {
                                                const components = netWorkList[nIndex].components
                                                component = components.find(com => com.Number === currentSelectPositionBlockNumber)

                                                if(component)
                                                {
                                                    break
                                                }
                                            }
                                            
                                            mainControl.currentSelectBlockType = getBlockType(component)
                                            mainControl.currentSelectBolckData = component
                                            copyOrCutType = getBlockType(blockData)
                                            mainControl.paste(parseInt(netWorkControl.currentSelectPositionBlock.position), content)
                                            
                                            netWorkControl.currentSelectPositionBlock = {}
                                            ldManage.fixLDFileConnections(fileKey)
                                        }
                                    }
                                    onPositionChanged: {
                                        if(isLongPress && !isFlowEndBlock(getBlockType(blockData)))
                                        {
                                            if(startLongPressX !== 0 && startLongPressY !== 0)
                                            {
                                                // 计算与开始长按时的坐标的差值
                                                const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                                const deltaX = Math.abs(positionInRoot.x - startLongPressX)
                                                const deltaY = Math.abs(positionInRoot.y - startLongPressY)

                                                // 只有在长按的情况下并且大幅度移动时才会显示坐标块
                                                if(deltaX >= 20 || deltaY >= 20)
                                                {
                                                    mainControl.isShowPositionBlocks = true
                                                    const positionInNetWork = mapToItem(netWorkControl, mouse.x, mouse.y)
                                                    if(positionInNetWork.x < 0 || positionInNetWork.y < 0)
                                                    {
                                                        mainControl.isShowPositionBlocks = false
                                                    }
                                                    else
                                                    {
                                                        // 触发信号,重新计算坐标块的位置
                                                        calculatePositionBlockPoints(positionInNetWork.x, positionInNetWork.y)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 右边引脚
                Rectangle {
                    width: parent.width / 3
                    height: parent.height
                    color: "transparent"

                    // ENO引脚
                    Rectangle {
                        height: config.defaultLineWidth
                        width: parent.width
                        color: config.defaultConnectionLineColor
                        // 从当前块元件所在的y轴开始计算
                        // funBlockTextHeight 块元件文本高度
                        // cellCentreHeight EN和ENO的中间高度
                        y: control.y + funBlockTextHeight + cellCentreHeight - config.defaultLineWidth
                    }

                    // ENO引脚置反
                    Rectangle {
                        width: config.defaultLineWidth * 4
                        height: config.defaultLineWidth * 4
                        radius: height / 2
                        border.width: 1
                        border.color: "#000000"
                        color: "#ffffff"
                        anchors.left: parent.left
                        y: control.y + funBlockTextHeight + cellCentreHeight - height / 2 - config.defaultLineWidth / 2
                        visible: eno.Negated
                    }

                    // ENO引脚选中框
                    Rectangle {
                        width: parent.width / 5
                        height: config.defaultLineWidth * 3
                        color: parentFocus && focus ? "#0b59e3" : "transparent"
                        opacity: 0.5
                        anchors.left: parent.left
                        y: control.y + funBlockTextHeight + cellCentreHeight - height / 2 - config.defaultLineWidth / 2

                        MouseArea {
                            anchors.fill: parent
                            acceptedButtons: Qt.LeftButton | Qt.RightButton
                            onClicked: {
                                activeFocusItem = "PIN"
                                currentSelectBlockType = ""
                                currentSelectBolckData = {}
                                parent.forceActiveFocus()
                                mainControl.startBlockNumber = control.startBlockNumber
                                mainControl.netWorkNumber = control.netWorkNumber
                                if (mouse.button === Qt.RightButton)
                                {
                                    const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                    mainControl.showMenu(positionInRoot.x, positionInRoot.y, "PIN", eno.Name.toLowerCase(), eno)
                                }
                                else
                                {
                                    mainControl.currentSelectBolckData = eno
                                    mainControl.currentSelectBlockType = eno.Name.toLowerCase()
                                }
                            }
                        }
                    }

                    // Output引脚
                    Repeater {
                        model: outputs
                        Rectangle {
                           anchors.right: parent.right
                           anchors.rightMargin: 5
                           width: parent.width - 5
                           height: pinHeight
                           color: "transparent"
                           y: control.y + funBlockTextHeight + cellCentreHeight + ((index + 1) * pinHeight) - height / 2 - config.defaultLineWidth / 2 + index * 2

                            Row {
                              anchors.fill: parent
                              // 引脚线条
                              Rectangle {
                                 width: parent.width / 5
                                 height: config.defaultLineWidth
                                 y: parent.height / 2
                                 color: config.defaultConnectionLineColor
                              }

                              // 引脚文本框
                              Rectangle {
                                 width: parent.width - parent.width / 5
                                 height: parent.height
                                 border.width: output_pin_text.activeFocus ? 1 : config.defaultLineWidth
                                 border.color: config.defaultConnectionLineColor
                                 color: output_pin_text.activeFocus ? "#6495ed" : "transparent"

                                 TextInput {
                                    id: output_pin_text
                                    anchors.fill: parent
                                    font.pixelSize: config.fontPixelSize
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    text: outputs[index].PinData.AuxContent
                                    color: "black"
                                    clip: true
                                    selectByMouse: true
                                    selectionColor: config.defaultSelectionColor
                                    selectedTextColor: config.defaultSelectedTextColor

                                    MouseArea {
                                        anchors.fill: parent
                                        acceptedButtons: Qt.LeftButton
                                        cursorShape: Qt.OpenHandCursor
                                        onClicked: {
                                            if (mouse.button === Qt.LeftButton || mouse.button === Qt.RightButton)
                                            {
                                                //显示变量弹窗 含全局变量 局部变量 IO变量 常量?
                                                var newCData = {
                                                    "NetworkNumber": netWorkNumber,
                                                    "VarCompNumber": outputs[index].PinData.Number,
                                                    "DataType": outputs[index].PinData.ParentPinDataType,
                                                    "LeftOrRight": 1,
                                                    "AuxContent": outputs[index].PinData.AuxContent
                                                }
                                                showSelectVariableDialog(newCData)
                                            }
                                        }
                                    }
                                 }
                              }
                            }

                            // 引脚置反
                            Rectangle {
                                width: config.defaultLineWidth * 4
                                height: config.defaultLineWidth * 4
                                radius: height / 2
                                border.width: 1
                                border.color: "#000000"
                                color: "#ffffff"
                                anchors.left: parent.left
                                y: parent.height / 2 - ((height - config.defaultLineWidth) / 2)
                                visible: outputs[index].Negated
                            }

                            // 引脚选中框
                            Rectangle {
                                width: parent.width / 5
                                height: config.defaultLineWidth * 3
                                color: parentFocus && focus ? "#0b59e3" : "transparent"
                                opacity: 0.5
                                anchors.left: parent.left
                                y: parent.height / 2 - ((height - config.defaultLineWidth) / 2)

                                MouseArea {
                                    anchors.fill: parent
                                    acceptedButtons: Qt.LeftButton | Qt.RightButton
                                    onClicked: {
                                        activeFocusItem = "PIN"
                                        currentSelectBlockType = ""
                                        currentSelectBolckData = {}
                                        parent.forceActiveFocus()
                                        mainControl.startBlockNumber = control.startBlockNumber
                                        mainControl.netWorkNumber = control.netWorkNumber
                                        if (mouse.button === Qt.RightButton)
                                        {
                                            const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                                            mainControl.showMenu(positionInRoot.x, positionInRoot.y, "PIN", outputs[index].Direction.toLowerCase(), outputs[index])
                                        }
                                        else
                                        {
                                            mainControl.currentSelectBolckData = outputs[index]
                                            mainControl.currentSelectBlockType = outputs[index].Direction.toLowerCase()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    function getName(direction)
    {
        const connectors = blockData.connectors

        for(let cIndex = 0; cIndex < connectors.length; cIndex++)
        {
            const connector = connectors[cIndex]

            if(direction === connector.Direction)
            {
                return connector.Name
            }
        }

        return ""
    }
}