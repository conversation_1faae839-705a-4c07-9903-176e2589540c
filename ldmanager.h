﻿/*
 * @Author: liquan
 * @Date: 2025-04-25 15:31:09
 * @Last Modified by: liquan
 * @Last Modified time: 2025-07-31 16:03:57
 */

#ifndef LD_MANAGER_H
#define LD_MANAGER_H

#include "extendfbc.h"
#include "idev1_ldeditor_global.h"
#include "ldobject.h"
#include "xmldata.h"
#include <QJsonArray>
#include <QJsonObject>
#include <QObject>
#include <QStack>

class IDELDEDITOR_EXPORT LDManager : public QObject
{
    Q_OBJECT
  public:
    LDManager() {};
    static LDManager &instance();

    // 初始化实例赋值当前应用目录 包括完成数据类型兼容文件读取的初始化
    void init(const QString &appPath);

    // 打开项目
    void OpenProject(const QString &pName, const QString &pPath);

    // 关闭当前项目
    void CloseProject();

    // 初始化文件原始内容
    Q_INVOKABLE QString initFile(const QString &fileName, const QString &fileType, const QString &codeType);

    // 读取文件 fileType= Core1 Core2 UserFBs
    Q_INVOKABLE bool readFile(const QString &deviceName, const QString &fileType, const QString &fileName,
                              const QString &filePath);

    // 读取文件
    bool readFile(const QString &deviceName, const QString &fileType, const QString &fileName, const QString &filePath,
                  const QByteArray &bytes);

    // 获取指定文件数据
    QByteArray getFileData(const QString &fileKey);

    LDFile *getFile(QString path);

    // 保存文件
    Q_INVOKABLE bool saveFile(const QString &fileKey);

    // 保存文件内容到内存
    Q_INVOKABLE bool saveFile(const QString &fileKey, QJsonArray data);

    // 全部文件保存
    Q_INVOKABLE void saveAllFile();

    // 获取文件基本信息
    Q_INVOKABLE QJsonObject getPlanInfo(const QString &fileKey);

    // 修改该文件所有块的绑定任务名称
    Q_INVOKABLE void modifyTaskName(const QString &fileKey, const QString taskName);

    // 修改文件的作者和备注信息
    Q_INVOKABLE void updateAuthorComment(const QString &fileKey, const QString &author, const QString &comment);

    // 获取文件中的所有网络 画网络
    Q_INVOKABLE QJsonArray getNetwork(const QString &fileKey);

    // 获取文件中的所有网络，块，引脚
    Q_INVOKABLE QJsonArray getAllInfo(const QString &fileKey);

    // 获取指定网络中的块和引脚 画块
    Q_INVOKABLE QJsonArray getComponentConnector(const QString &fileKey, int networkNumber);

    // 获取文件中指定网络的链接 画线
    Q_INVOKABLE QJsonArray getNetworkConnections(const QString &fileKey, int networkNumber);

    // 获取文件中所有的功能块名称和任务周期,用来调整块的执行顺序
    Q_INVOKABLE QJsonArray getAllBlockName(const QString &fileKey);

    // 添加网络  beforeOrAfter = 0 在之前添加 / 1 在之后添加
    Q_INVOKABLE bool addNetwork(const QString &fileKey, int networkNumber, int beforeOrAfter);

    // 在某个网络之前添加一个网络
    Q_INVOKABLE bool addNetworkBefore(const QString &fileKey, int networkNumber);

    // 在某个网络之后添加一个网络
    Q_INVOKABLE bool addNetworkAfter(const QString &fileKey, int networkNumber);

    // 删除网络
    Q_INVOKABLE bool deleteNetwork(const QString &fileKey, int networkNumber);

    // 复制网络
    Q_INVOKABLE QJsonObject copyNetwork(const QString &fileKey, int networkNumber);

    // 粘贴网络  beforeOrAfter = 0 在之前粘贴 / 1 在之后粘贴（默认）
    Q_INVOKABLE bool pasteNetwork(const QString &fileKey, int networkNumber, const QJsonObject &networkData,
                                  int beforeOrAfter = 1);

    // 剪切网络
    Q_INVOKABLE QJsonObject cutNetwork(const QString &fileKey, int networkNumber);

    // 修改网络标签
    Q_INVOKABLE bool modifyNetworkLabel(const QString &fileKey, int number, const QString &newLabel);

    // 修改网络注释
    Q_INVOKABLE bool modifyNetworkComment(const QString &fileKey, int number, const QString &newComment);

    // 修改网络使能
    Q_INVOKABLE bool modifyNetworkEnable(const QString &fileKey, int number, const bool enable);

    // 修改元件的名称
    Q_INVOKABLE bool modifyComponentName(const QString &fileKey, int compNumber, const QString &newName);

    // 修改元件的引脚的类型
    Q_INVOKABLE bool modifyComponentConnectorType(const QString &fileKey, int compNumber, int pinId,
                                                  const QString &DataType);

    // 更改变量元件变量绑定
    Q_INVOKABLE bool modifyVariableComponent(const QString &fileKey, int networkNumber, int compNumber,
                                             const QString &scope, const QString &name, const QString &owned);

    // 清除元件的变量绑定
    Q_INVOKABLE bool clearVariableComponent(const QString &fileKey, int networkNumber, int compNumber);

    // 添加水平串联触点
    Q_INVOKABLE int addSeriesContact(const QString &fileKey, int compNumber, int leftOrRight);

    // 添加并联触点
    Q_INVOKABLE int addParallelContact(const QString &fileKey, int compNumber);

    // 复制触点/线圈/跳转/块/...等元件(包含挂载变量和引脚信息)
    Q_INVOKABLE QJsonObject copyComponent(const QString &fileKey, int compNumber);

    // 根据JSON中的connectors信息查找或创建引脚
    QSharedPointer<LDConnector> findOrCreateConnectorFromJson(QSharedPointer<LDComponent> component, int pinId,
                                                              const QJsonArray &componentsArray);

    // 水平粘贴触点
    Q_INVOKABLE bool pasteSeriesContact(const QString &fileKey, int compNumber, const QJsonObject &componentData,
                                        int leftOrRight);

    // 并联粘贴触点
    Q_INVOKABLE bool pasteParallelContact(const QString &fileKey, int compNumber, const QJsonObject &componentData);

    // 粘贴线圈/跳转/返回/Set1/Set0
    Q_INVOKABLE bool pasteCoilJumpReturn(const QString &fileKey, int compNumber, const QJsonObject &componentData);

    // 粘贴块/扩展块
    Q_INVOKABLE bool pasteBlockComponent(const QString &fileKey, int compNumber, const QJsonObject &componentData,
                                         int leftOrRight);

    // 剪切触点/线圈/跳转/块/...等元件(包含挂载变量和引脚信息)
    Q_INVOKABLE QJsonObject cutComponent(const QString &fileKey, int compNumber);

    // 删除触点
    Q_INVOKABLE bool deleteContact(const QString &fileKey, int compNumber);

    // 添加线圈/跳转/返回/Set1/Set0 用户只能选择末尾位置插入
    Q_INVOKABLE int addCoilComponent(const QString &fileKey, int networkNumber, int compNumber,
                                     const QString &AuxContent);

    // 删除线圈/跳转/返回 用户只能选择末尾位置删除
    Q_INVOKABLE bool deleteCoilComponent(const QString &fileKey, int networkNumber, int compNumber);

    // 添加块元件/扩展块元件
    Q_INVOKABLE int addBlockComponent(const QString &fileKey, int networkNumber, int compNumber, QJsonObject param,
                                      int leftOrRight);

    // 添加的块引脚绑定的变量元件 返回变量元件的Number 0失败
    int createNewVariableComponent(const QString &fileKey, int networkNumber, int leftOrRight, int parentNumber,
                                   int parentPinId, const QString &pinDataType, int xpos, int ypos);

    // 创建块元件
    QSharedPointer<LDComponent> createBlockComponent(const QString &fileKey, int networkNumber, QJsonObject param);

    // 创建扩展元件
    QSharedPointer<LDComponent> createExtendComponent(const QString &fileKey, int networkNumber, QJsonObject param);

    // 在指定元件右侧添加块元件
    bool addBlockComponentToRight(const QString &fileKey, int networkNumber, int compNumber,
                                  QSharedPointer<LDComponent> newComp);

    // 在指定元件左侧添加块元件
    bool addBlockComponentToLeft(const QString &fileKey, int networkNumber, int compNumber,
                                 QSharedPointer<LDComponent> newComp);

    // 删除块元件
    Q_INVOKABLE bool deleteBlockComponent(const QString &fileKey, int networkNumber, int compNumber);

    // 修复梯形图中的连接关系
    Q_INVOKABLE bool fixLDFileConnections(const QString &fileKey);

    //(扩展)元件引脚取反/置反操作
    Q_INVOKABLE bool modifyConnectorNegated(const QString &fileKey, int compNumber, int pinId);

    // 更改元件注释
    Q_INVOKABLE bool modifyComponentComment(const QString &fileKey, int compNumber, const QString &comment);

    // 置位:将线圈的AuxContent:Coil设置为AuxContent:Set1
    Q_INVOKABLE bool setCoilToSet1(const QString &fileKey, int compNumber);

    // 复位:将线圈的AuxContent:Coil设置为AuxContent:Set0
    Q_INVOKABLE bool resetCoilToSet0(const QString &fileKey, int compNumber);

    // 重置:将Set1/Set0的AuxContent设置为Coil
    Q_INVOKABLE bool resetToCoil(const QString &fileKey, int compNumber);

    // 添加引脚并绑定变量
    Q_INVOKABLE bool InsertConnectorAndVariable(const QString &fileKey, int compNumber, int pinId);

    // 删除指定元件的指定引脚
    Q_INVOKABLE bool componentDeleteConnector(const QString &fileKey, int compNumber, int pinId);

    // 判断选中位置是否能添加线圈/跳转/返回元件
    Q_INVOKABLE bool canAddCoilJumpReturn(const QString &fileKey, int networkNumber, int compNumber);

    // 查找水平路径上的第一个节点
    QSharedPointer<LDComponent> findFirstNodeInHorizontalPath(LDFile *ld, int compNumber);

    // 查找水平串联的后继元件
    QSharedPointer<LDComponent> findSerialSuccessor(LDFile *ld, int compNumber);

    // 修复块元件链接
    bool fixBlockConnection(LDFile *ld, int networkNumber);

    // 恢复块元件链接
    bool restoreBlockConnection(LDFile *ld, int networkNumber);

    // 判断元件是否是用户自定义元件
    bool isUserDefinedComponent(LDFile *ld, QSharedPointer<LDComponent> com_ptr);

  private:
    // 为网络新增添加默认起始常量元件 返回新增的启动常量元件的Number
    int putStartOnEN(const QString &fileKey, int networkNumber);

    // 获取某个元件所在x轴的最后一个元件
    QSharedPointer<LDComponent> getLastComponent(const QString &fileKey, int networkNumber, int compNumber);

    // 获取y=0轴的最后一个OR块
    QSharedPointer<LDComponent> getLastORBlock(LDFile *ld, int networkNumber);

    // 清理无效连接（如源组件号或目标组件号为0的连接）
    bool cleanInvalidConnections(const QString &fileKey);

    // 在元件左侧添加水平串联元件（辅助函数）
    QSharedPointer<LDComponent> addSeriesContactToLeft(const QString &fileKey, QSharedPointer<LDComponent> com_ptr,
                                                       const QString compType);

    // 在元件右侧添加水平串联元件（辅助函数）
    QSharedPointer<LDComponent> addSeriesContactToRight(const QString &fileKey, QSharedPointer<LDComponent> com_ptr,
                                                        const QString compType);

    // 添加链接，链接两个指定的引脚
    QSharedPointer<LDConnection> addConnection(const QString &fileKey, int sourceCompNumber, int sourcePinId,
                                               int sourceConnectIndex, int targetCompNumber, int targetPinId,
                                               int targetConnectIndex, bool visible);

    // 断开连接
    bool deleteConnection(const QString &fileKey, int sourceCompNumber, int sourcePinId, int targetCompNumber,
                          int targetPinId);

    // 检查源引脚是否已有其他连接（连接到任何目标）结果如果存在，重用现有连接的实例名称和变量信息
    QStringList haveConnectionFromSourcePin(const QString &fileKey, int sourceComponentNumber, int sourcePinId);

    // 向指定元件添加EN引脚和ENO引脚
    bool addENAndENOConnector(const QString &fileKey, int compNumber);

    // 向指定元件添加IN引脚
    int addINConnector(const QString &fileKey, int compNumber);

    // 向指定元件添加OUT引脚
    int addOUTConnector(const QString &fileKey, int compNumber);

    // 向指定元件的指定引脚位置下方插入IN引脚
    QSharedPointer<LDConnector> insertINConnector(const QString &fileKey, int compNumber, int pinId);

    // 向指定元件的指定引脚位置下方插入OUT引脚
    QSharedPointer<LDConnector> insertOUTConnector(const QString &fileKey, int compNumber, int pinId);

    // 获取起始常量元件
    QSharedPointer<LDComponent> getStartConstant(const QString &fileKey, int networkNumber = -1);

    // 将指定元件的EN引脚与启动常量元件的ENO引脚连接
    bool connectENToStart(const QString &fileKey, int compNumber);

    // 获取指定元件的第一个IN引脚
    QSharedPointer<LDConnector> getFirstINConnector(const QString &fileKey, int compNumber);

    // 获取指定元件的第一个OUT引脚
    QSharedPointer<LDConnector> getFirstOUTConnector(const QString &fileKey, int compNumber);

    // 获取指定元件的最后一个OUT引脚
    QSharedPointer<LDConnector> getLastOUTConnector(const QString &fileKey, int compNumber);

    // 获取元件的第一个输出引脚
    QSharedPointer<LDConnector> getFirstOutputConnector(const QString &fileKey, int compNumber);

    // 构造AND块
    QSharedPointer<LDComponent> createAndBlock(const QString &fileKey, QSharedPointer<LDComponent> component);

    // 构造新变量元件绑定到指定元件
    bool connectVariableToComponent(const QString &fileKey, QSharedPointer<LDComponent> parentComponent);

    // 构造新变量元件绑定到指定元件的指定引脚
    bool connectVariableToComponent(const QString &fileKey, QSharedPointer<LDComponent> parentComponent, int pinId);

    // 构造OR块
    QSharedPointer<LDComponent> createOrBlock(const QString &fileKey, QSharedPointer<LDComponent> component);

    // 构造Coil块
    QSharedPointer<LDComponent> createCoilBlock(const QString &fileKey, QSharedPointer<LDComponent> component);

    // 构造功能块(And/Or/Coil)
    QSharedPointer<LDComponent> createBlock(const QString &fileKey, QSharedPointer<LDComponent> parentComponent,
                                            const QString &blockType);

    // 根据输出引脚获取对应的连接
    QSharedPointer<LDConnection> getOutputConnectorLDConnection(const QString &fileKey, int compNumber, int pinId);

    // 根据输入引脚获取对应的连接
    QSharedPointer<LDConnection> getInputConnectorLDConnection(const QString &fileKey, int compNumber, int pinId);

    // 获取连接到指定组件输入的OR节点的输出引脚(入口函数)
    QSharedPointer<LDConnector> getConnectedORNodeConnector(const QString &fileKey, int compNumber);

    // 递归辅助函数：从给定组件向上追踪到OR节点
    QSharedPointer<LDConnector> getConnectedORNodeConnectorRecursive(LDFile *ld, int compNumber,
                                                                     QSet<int> &visitedComponents);

    // 获取两个元件之间的连接
    QSharedPointer<LDConnection> getConnection(const QString &fileKey, int compNumber1, int compNumber2);

    // 查找水平串联的前驱元件
    QSharedPointer<LDComponent> findSerialPredecessor(LDFile *ld, int compNumber);

    // 删除某个元件未被使用的输出引脚，修改已使用的引脚id从大到小重新排序
    void deleteExtraOutConnectors(const QString &fileKey, int compNumber);

    // 删除某个元件未被使用的输入引脚，修改已使用的引脚id从小到大重新排序
    void deleteExtraInConnectors(const QString &fileKey, int compNumber);

    // 重新排序连接索引，确保SourceConnectIndex和TargetConnectIndex的连续性
    void reorderConnectionIndexes(const QString &fileKey, const QList<int> &affectedComponents);

    // 调整现有连接的索引，确保新连接插入到正确位置
    void adjustExistingConnectionIndexes(const QString &fileKey, int sourceCompNumber, int newTargetCompNumber,
                                         int newSourceIndex, int newTargetIndex);

    // 调整OR块输入连接的索引
    void adjustORBlockInputIndexes(const QString &fileKey, int orBlockNumber, int insertIndex);

    // 删除行末尾OR块 传入为OR块的Number
    void deleteEndORBlock(const QString &fileKey, int compNumber);

    // 删除连接 某个元件作为输出元件的连接
    void deleteOutputConnection(const QString &fileKey, int compNumber);

    // 查找与指定组件相关的变量组件
    QList<QSharedPointer<LDComponent>> findRelatedVariableComponents(const QString &fileKey, int compNumber);

    // 从JSON数据创建组件
    QSharedPointer<LDComponent> createComponentFromJson(const QJsonObject &componentJson, const QString &fileKey,
                                                        int networkNumber);

    // 重新分配引脚ID
    void reassignPinIds(QSharedPointer<LDComponent> component);

    // 从剪贴板数据创建组件
    QSharedPointer<LDComponent> createComponentFromClipboard(const QJsonObject &componentJson, const QString &fileKey,
                                                             int networkNumber);

    // 收集网络内部的所有连接
    QJsonArray collectNetworkConnections(const QString &fileKey, int networkNumber);

    // 从JSON重建网络组件
    QMap<int, int> rebuildNetworkComponents(const QString &fileKey, int newNetworkNumber,
                                            const QJsonArray &componentsArray);

    // 重建网络连接
    bool rebuildNetworkConnections(const QString &fileKey, const QJsonArray &connectionsArray,
                                   const QMap<int, int> &componentNumberMap);

    // 获取下一个可用的网络编号
    int getNextAvailableNetworkNumber(const QString &fileKey, int afterNetworkNumber);

    // 判断某个块是否是另一个块的父块/祖父块
    bool isParentOrGrandparent(LDFile *ld, int sonNumber, int parentNumber);

    // 修改元件的编号
    bool modifyComponentNumber(const QString &fileKey, int compNumber, int newNumber);

    // 获取UUID
    QString getUUID();

    // 组件类型验证辅助函数
    bool isSeriesContactType(const QJsonObject &componentJson);
    bool isParallelContactType(const QJsonObject &componentJson);
    bool isCoilJumpReturnType(const QJsonObject &componentJson);
    bool isBlockComponentType(const QJsonObject &componentJson);

    // 获取某个块的最大SourceConnectIndex
    int getMaxSourceConnectIndex(const QString &fileKey, int compNumber);
    // 获取某个块的最大TargetConnectIndex
    int getMaxTargetConnectIndex(const QString &fileKey, int compNumber);

  signals:
    // 文件变化
    void fileChanged(QString filekey);
    // 网络变化
    void networkChanged(QString filekey, int networknumber, QJsonArray data);
    // 元件变化
    void componentChanged(QString filekey, int networknumber, int componentNumber, QJsonArray data);
    // 引脚变化
    void connectorChanged(QString filekey, int networknumber, int componentNumber, int connectorPid, QJsonArray data);
    // 连线变化
    void lineChanged(QString filekey, int networknumber, QJsonArray data);

  private:
    // 应用目录
    QString appDir;
    // 当前项目名称
    QString projectName;
    // 当前项目目录
    QString projectDir;
    // 所有LD文件
    QMap<QString, QSharedPointer<LDFile>> LDFileList;
    // 高级扩展函数
    QSharedPointer<ExtendFBC> extendFBC;
};

#endif // CommonManage_H