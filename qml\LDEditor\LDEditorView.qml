﻿import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs 1.3
import FluentUI 1.0
import "qrc:/qml/gv"
import "qrc:/qml/skin"
import "qrc:/qml/control/common"
import "qrc:/qml/control/menu"
import "qrc:/SimpleUI/qml"

Item {
    id: root
    property var dataTypeList: []
    property var scopeList: ["Local"]
    //根据文件类型有多种 PROGRAM FUNCTION FUNCTIONBLOCK
    property int fileId: 0
    property string deviceName: ""
    property string fileKey: ""
    property string fileName: ""
    property string fileType: ""
    property string fileCode: ""
    // 主控件
    property var multiControl
    // 缩放比例
    property int scaleFactor: 100
    // 是否需要保存文件
    property bool isConserve: false

    SplitView {
        id: splitView
        anchors.fill: parent
        orientation: Qt.Vertical

        ColumnLayout {
            id: quote_table
            spacing: 0
            SplitView.minimumHeight: 50
            SplitView.preferredHeight: (ldEditor.height - 30) / 2

            //搜索栏和操作按钮
            RowLayout {
                id: action_bar

                Layout.fillWidth: true
                Layout.maximumHeight: 36
                Layout.leftMargin: 10
                Layout.rightMargin: 10
                spacing: 15

                QkButton {
                    id: input_add

                    text: "添加"
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 25
                    onClicked: {
                        varInputArea.addVariable()
                    }
                }

                QkButton {
                    id: btn_delete

                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 25
                    text: "删除"

                    onClicked: {
                        varInputArea.deleteVariable()
                    }
                }

                Item {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                }

                QkTextField {
                    id: input_search
                    Layout.preferredWidth: 180
                    Layout.preferredHeight: 30
                    selectByMouse: true
                    text: ""

                    onTextChanged: {
                        varInputArea.searchVariable(input_search.text)
                    }
                }

                QkButton {
                    id: search_btn
                    text: "搜索"
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 25
                    onClicked: {
                        varInputArea.searchVariable(input_search.text)
                    }
                }
            }

            //上部变量输入框
            VariableInput {
                id: varInputArea

                Layout.fillWidth: true
                Layout.fillHeight: true

                deviceName: root.deviceName
                owned: root.fileName
                type: root.fileType
                dataTypeList: root.dataTypeList
                scopeList: root.scopeList
            }
        }

        handle: Item {
            implicitHeight: 5
            Rectangle {
                anchors.fill: parent
                color: "lightgray"
                border.color: "black"
                border.width: 1
            }
        }

        Rectangle {
            SplitView.preferredHeight: 600
            SplitView.minimumHeight: 200
            SplitView.maximumHeight: parent.height
            color: "white"
            LDEditor {
                id: ldEditor
                fileKey: root.fileKey
                owned: root.fileName
                type: root.fileType
                deviceName: root.deviceName
                isConserve: root.isConserve
            }

            // Connections {
            //     target: ldEditor
            //     function onUpdateFileFlag(flag) {
            //         updateFileFlag(flag)
            //     }
            // }
        }
    }

    // 显示指定块名称所在的位置
    function revealPosition(blockName) {
        console.log("blockName:", blockName)
    }

    //绑定所有数据
    function getDataBind() {
        // 根据文件类型设置数据范围
        if (root.fileType === "FUNCTIONBLOCK")
            root.scopeList.push("Input", "Output", "Static")
        else if (root.fileType === "FUNCTION")
            root.scopeList.push("Input", "Output")

        // 筛选出基础类型和用户定义的类型
        root.dataTypeList = VariableManage.getDataType(root.deviceName).filter(
                    dataType => {
                        if (root.fileType === "FUNCTION") {
                            return (dataType.type === "BASE"
                                    && dataType.dataType !== "STRUCT")
                            || dataType.type === "USER"
                        } else {
                            return dataType.type === "BASE"
                            || dataType.type === "USER"
                        }
                    })

        varInputArea.getDataBind()
        //root.functionBlockTaskNameDataBind()
        ldEditor.getDataBind()
        //ldEditor.getAllBlockFunction()
    }

    //调用C++接口保存文件
    function savefile(fid, path) {
        if (root.fileKey === path) {
            //检查
            ldManage.saveFile(root.fileKey)
            root.multiControl.updateFileFlag(path, false)
        }
    }
}
